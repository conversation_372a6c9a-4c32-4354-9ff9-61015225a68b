"use client";
import React, { useState, useEffect, useRef } from "react";
import NotificationDropdownItem from "./NotificationDropdownItem";
import Button from "~/component/button";
import { FaBell } from "react-icons/fa";
import { NotificationType } from "@prisma/client";

export interface SystemNotification {
  id: string;
  datum: Date;
  type: NotificationType;
  nachricht: string;
  gelesen: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface NotificationDropdownProps {
  notifications: SystemNotification[];
  unreadCount: number;
  onMarkAsRead: (id: string) => void;
  onMarkAllAsRead: () => void;
  onRefresh: () => void;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  notifications,
  unreadCount,
  onMarkAsRead,
  onMarkAllAsRead,
  onRefresh,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleToggle = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      onRefresh(); // Refresh notifications when opening
    }
  };

  return (
    <li className="relative flex items-center" ref={dropdownRef}>
      {/* Bell Icon with Badge */}
      <div className="relative cursor-pointer " onClick={handleToggle}>
        <FaBell
          size={20}
          className="ease-nav-brand block p-0 text-primary transition-colors hover:brightness-90 dark:text-white"
        />
        {unreadCount > 0 && (
          <span className="absolute -right-1 -top-1 flex h-4 w-4 items-center justify-center rounded-full bg-red-500 text-xs font-bold text-white">
            {unreadCount > 9 ? "9+" : unreadCount}
          </span>
        )}
      </div>

      {/* Dropdown */}
      <div
        className={` absolute left-auto right-0 top-full z-50 mt-2 w-80 list-none rounded-xl bg-white shadow-2xl shadow-soft-3xl transition-all duration-200 before:absolute before:-top-2 before:right-5 before:z-20 before:border-b-8 before:border-l-8 before:border-r-8 before:border-t-0 before:border-solid before:border-transparent before:border-b-white before:content-[''] dark:bg-gray-800 dark:before:border-b-gray-800 ${
          isOpen ? "opacity-100" : " opacity-0"
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 px-4 py-3 dark:border-gray-700">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
            Benachrichtigungen
          </h3>
          {unreadCount > 0 && (
            <Button onClick={onMarkAllAsRead} className="px-2 py-1 text-xs">
              Alle als gelesen
            </Button>
          )}
        </div>

        {/* Notifications List */}
        <div className="max-h-80 overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="px-4 py-6 text-center text-sm text-gray-500 dark:text-gray-400">
              Keine Benachrichtigungen
            </div>
          ) : (
            notifications.map((notification) => (
              <NotificationDropdownItem
                key={notification.id}
                notification={notification}
                onMarkAsRead={onMarkAsRead}
              />
            ))
          )}
        </div>

        {/* Footer with "View All" link */}
        {notifications.length > 0 && (
          <div className="border-t border-gray-200 dark:border-gray-700 px-4 py-3">
            <a
              href="/notifications"
              className="block text-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium"
              onClick={() => setIsOpen(false)}
            >
              Alle Benachrichtigungen anzeigen
            </a>
          </div>
        )}
      </div>
    </li>
  );
};

export default NotificationDropdown;
