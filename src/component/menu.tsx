import Link from "next/link";

import React from "react";

import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { menuDef, MenuType } from "~/utils/menu/menuDef";
import MenuEntry from "~/component/MenuEntry";

const Menu = async () => {
  const session = await getServerSession(authOptions);

  const role = session?.user?.role;
  const visibleMenuItems = menuDef.filter(
    (menuDef) => session?.user?.role && menuDef.role.includes(session.user.role),
  );

  if (!session) {
    return <></>;
  }

  if (!role) {
    return <></>;
  }

  return (
    <>
      <ul>
        {visibleMenuItems.map((menuItem) => {
          return (
            <MenuEntry
              key={menuItem.name}
              menuItem={menuItem}
              visibleMenuItems={visibleMenuItems}
            />
          );
        })}
      </ul>
    </>
  );
};

export default Menu;
