"use client";

import { useRouter } from "next/navigation";
import React from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import Button from "~/component/button";

type FormData = {
  firstName: string;
  lastName: string;
  email: string;
  identifier?: string;
};

const InviteForm: React.FC = () => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>();
  const router = useRouter(); // Hier initialisieren wir den Router

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    try {
      const response = await fetch("/api/user/invite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      const result = await response.json();

      if (response.ok) {
        router.push("/users"); // Hier leiten wir um, wenn die Antwort erfolgreich ist
        router.refresh();
      } else {
        console.error("Error:", result.error);
      }
    } catch (error) {
      console.error("Error:", error);
    }
  };

  return (
    <div>
      <h2 className="mb-4 text-2xl font-bold">Personen-Einladung</h2>
      <p className="mb-6">
        Mit diesem Formular können Sie Personen einladen. Nachdem Sie die Einladung abgesendet
        haben, erhält die eingeladene Person eine E-Mail mit einem Link zur Registrierung. Die
        Registrierung über diesen Link ist nur für die eingeladene Person gültig und wird nach
        erfolgreicher Registrierung ungültig.
      </p>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div>
          <label className="block">
            Vorname<span className={"text-red-500"}>*</span>
          </label>
          <input
            {...register("firstName", { required: true })}
            className="block w-full appearance-none rounded-lg border
             border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
          />
          {errors.firstName && <span className="text-red-500">Dieses Feld ist erforderlich</span>}
        </div>
        <div>
          <label className="block">
            Nachname<span className={"text-red-500"}>*</span>
          </label>
          <input
            {...register("lastName", { required: true })}
            className="block w-full appearance-none rounded-lg border
             border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
          />
          {errors.lastName && <span className="text-red-500">Dieses Feld ist erforderlich</span>}
        </div>
        <div>
          <label className="block">
            E-Mail<span className={"text-red-500"}>*</span>
          </label>
          <input
            {...register("email", {
              required: true,
              pattern: /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/,
            })}
            placeholder={"Wird zum einloggen verwendet und kann nicht geändert werden"}
            className="block w-full appearance-none rounded-lg border
             border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
          />
          {errors.email && (
            <span className="text-red-500">Geben Sie eine gültige E-Mail-Adresse ein</span>
          )}
        </div>
        <div>
          <label className="block">Interne Kennung (optional)</label>
          <input
            {...register("identifier")}
            placeholder={"z.B. Personalnummer oder Mitgliedernummer"}
            className="block w-full appearance-none rounded-lg border
             border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
          />
        </div>
        <Button className={"w-full sm:max-w-64"} type="submit">
          Einladen
        </Button>
      </form>
    </div>
  );
};

export default InviteForm;
