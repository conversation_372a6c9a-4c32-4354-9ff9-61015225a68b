"use client";
import React, { useState } from "react";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import Button from "~/component/button";
import { FaPaperPlane, FaUser, FaEnvelope, FaComment } from "react-icons/fa";

interface ContactFormData {
  name: string;
  email: string;
  subject: string;
  message: string;
  priority: "low" | "medium" | "high";
}

const ContactForm: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<ContactFormData>({
    defaultValues: {
      priority: "medium",
    },
  });

  const onSubmit: SubmitHandler<ContactFormData> = async (data) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch("/api/support/contact", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        setSuccess("Ihre Nachricht wurde erfolgreich gesendet. Wir werden uns bald bei Ihnen melden.");
        reset();
      } else {
        const errorData = await response.json();
        setError(errorData.error || "Fehler beim Senden der Nachricht");
      }
    } catch (err) {
      setError("Netzwerkfehler beim Senden der Nachricht");
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-600";
      case "medium":
        return "text-yellow-600";
      case "low":
        return "text-green-600";
      default:
        return "text-gray-600";
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case "high":
        return "🔴 Hoch";
      case "medium":
        return "🟡 Mittel";
      case "low":
        return "🟢 Niedrig";
      default:
        return priority;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center mb-4">
        <FaPaperPlane className="mr-2 text-blue-500" size={20} />
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Kontaktieren Sie uns
        </h2>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* Name */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <FaUser className="inline mr-2" />
            Name *
          </label>
          <input
            {...register("name", { required: "Name ist erforderlich" })}
            type="text"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Ihr vollständiger Name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <FaEnvelope className="inline mr-2" />
            E-Mail *
          </label>
          <input
            {...register("email", { 
              required: "E-Mail ist erforderlich",
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: "Ungültige E-Mail-Adresse"
              }
            })}
            type="email"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="<EMAIL>"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
          )}
        </div>

        {/* Subject */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Betreff *
          </label>
          <input
            {...register("subject", { required: "Betreff ist erforderlich" })}
            type="text"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Kurze Beschreibung Ihres Anliegens"
          />
          {errors.subject && (
            <p className="mt-1 text-sm text-red-600">{errors.subject.message}</p>
          )}
        </div>

        {/* Priority */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Priorität
          </label>
          <select
            {...register("priority")}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="low">{getPriorityLabel("low")}</option>
            <option value="medium">{getPriorityLabel("medium")}</option>
            <option value="high">{getPriorityLabel("high")}</option>
          </select>
        </div>

        {/* Message */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <FaComment className="inline mr-2" />
            Nachricht *
          </label>
          <textarea
            {...register("message", { required: "Nachricht ist erforderlich" })}
            rows={5}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Beschreiben Sie Ihr Anliegen ausführlich..."
          />
          {errors.message && (
            <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
          )}
        </div>

        {/* Success/Error Messages */}
        {success && (
          <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
            <p className="text-green-700 dark:text-green-300">{success}</p>
          </div>
        )}

        {error && (
          <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-700 dark:text-red-300">{error}</p>
          </div>
        )}

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={loading}
            className="px-6 py-2 bg-blue-500 hover:bg-blue-600 disabled:opacity-50"
          >
            {loading ? "Wird gesendet..." : "Nachricht senden"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ContactForm;
