import React from "react";
import Card from "~/component/card";
import ContactForm from "./components/ContactForm";

const SupportPage = () => {
  return (
    <>
      <h1 className="mb-4 text-2xl font-bold dark:text-white">
        Support & Kontakt
      </h1>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Contact Form */}
        <Card>
          <ContactForm />
        </Card>
        
        {/* Contact Information */}
        <Card>
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              Kontaktinformationen
            </h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                  Adresse
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  EULEKTRO GmbH<br />
                  Musterstraße 123<br />
                  12345 Musterstadt
                </p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                  Telefon
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  +49 (0) 123 456 789
                </p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                  E-Mail
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  <EMAIL>
                </p>
              </div>
              
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                  Öffnungszeiten
                </h3>
                <p className="text-gray-600 dark:text-gray-300">
                  Montag - Freitag: 9:00 - 17:00 Uhr<br />
                  Samstag - Sonntag: Geschlossen
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </>
  );
};

export default SupportPage;
