"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";

import React from "react";
import Card from "~/component/card";
import Headline from "~/component/Headline";
import { EMPCardAdminTable } from "~/app/(app)/emp/card/component/EMPCardAdminTable";
import { PhysicalCardForm } from "~/app/(app)/emp/card/component/PhysicalCardForm";
import { PhysicalCardTable } from "~/app/(app)/emp/card/component/PhysicalCardTable";

const getPhysicalAllCards = async () => {
  const session = await getServerSession(authOptions);

  if (!session) {
    return [];
  }

  const physicalCards = await prisma.physicalCard.findMany({
    include: { EMPCard: { include: { user: true } }, cpo: true },
  });
  return physicalCards;
};

const getCardsForAdmin = async () => {
  const session = await getServerSession(authOptions);

  if (!session) {
    return { empcards: [], physicalCards: [] };
  }

  const empcards = await prisma.eMPCard.findMany({
    include: { user: true, physicalCard: true, contact: true },
  });
  const physicalCards = await prisma.physicalCard.findMany({
    where: { EMPCard: null },
  });
  const cpos = await prisma.contact.findMany({ where: { cpo: true } });
  return { empcards: empcards ?? [], physicalCards: physicalCards ?? [], cpos: cpos ?? [] };
};

export const AdminCardPage = async () => {
  const { empcards, physicalCards, cpos } = await getCardsForAdmin();

  return (
    <>
      <Headline title={"Mitarbeiterladen - Ladekartenverwaltung"} />
      <div className={"flex flex-col gap-5"}>
        <Card header_left={"Bestellungen einsehen und bearbeiten"}>
          <EMPCardAdminTable empcards={empcards} physicalCards={physicalCards} />
        </Card>
        <Card header_left={"Neue Karten auslesen und anlegen"}>
          <PhysicalCardForm cpos={cpos ?? []} />
          <PhysicalCardTable physicalCards={(await getPhysicalAllCards()) ?? []} />
        </Card>
      </div>
    </>
  );
};
