"use client";
import React, { FC, startTransition, useRef, useState } from "react";
import { useForm, useField<PERSON><PERSON>y, Controller } from "react-hook-form";
import But<PERSON> from "~/component/button";
import { FaPlusCircle, FaTrash } from "react-icons/fa";

import { useRouter } from "next/navigation";
import Loading from "~/app/(app)/loading";
import { ContactsWithAddress } from "~/types/prisma/contact";
import { getFirstDayOfLastMonth, getLastDayOfLastMonth } from "~/utils/date/date";
import DatePicker from "react-datepicker";

// types/InvoiceFormTypes.ts
export interface ClientInvoicePosition {
  pos: number;
  title: string;
  unit: string;
  unitPrice: number;
  amount: number;
  description: string;
  taxRate: number;
}

export interface ClientInvoiceFormData {
  invoicePositions: ClientInvoicePosition[];
  startDate: string;
  endDate: string;
  contactId: string;
  totalSumNet: number;
  totalSumGross: number;
  kindOfInvoice: string;
}

export const CustomInvoiceForm = ({
  contacts,
  invoicePositions,
  startDate,
  endDate,
}: {
  contacts: ContactsWithAddress[];
  invoicePositions?: ClientInvoicePosition[];
  startDate?: string;
  endDate?: string;
}) => {
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState<string>("");
  const {
    formState: { isSubmitting, isSubmitSuccessful },
    register,
    getValues,
    setValue,
    watch,
    control,
    handleSubmit,
  } = useForm<ClientInvoiceFormData>({
    defaultValues: {
      invoicePositions: invoicePositions
        ? [...invoicePositions]
        : [{ pos: 1, title: "", unit: "", amount: 0, unitPrice: 0, description: "", taxRate: 19 }],
      startDate: startDate ?? "",
      endDate: endDate ?? "",
      contactId: contacts.length == 1 ? contacts[0]?.id : "",
    },
  });
  const watchedFields = watch("invoicePositions");
  const { fields, append, remove } = useFieldArray({
    control,
    name: "invoicePositions",
  });

  const onSubmit = async (data: ClientInvoiceFormData) => {
    setErrorMessage("");
    const response = await fetch("/api/invoice/prepare/customInvoice", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ ...data, totalSumNet: totalSumNet, totalSumGross: totalSumGross }),
    });
    if (response.ok && response.status == 200) {
      const { invoiceId } = await response.json();
      startTransition(() => {
        // Refresh the current route and fetch new data from the server without
        // losing client-side browser or React state.
        router.push(`/invoice/${invoiceId}`);
      });
    } else {
      setErrorMessage(response.statusText);
    }
  };

  const calcSum = (index: number, vat: boolean) => {
    const row = watchedFields[index];
    if (row) {
      if (vat && row.taxRate) {
        return (row.amount * row.unitPrice * (1 + row.taxRate / 100)).toFixed(2);
      }
      return (row.amount * row.unitPrice).toFixed(2);
    }
    return 0;
  };

  // Berechnen der Gesamtsumme
  const { net: totalSumNet, gross: totalSumGross } = watchedFields.reduce(
    (acc: { net: number; gross: number }, field) => {
      {
        acc.net += field.unitPrice * field.amount;
        if (field.taxRate) {
          acc.gross += field.unitPrice * field.amount * (1 + field.taxRate / 100);
        } else {
          acc.gross += field.unitPrice * field.amount;
        }

        return acc;
      }
    },
    { net: 0, gross: 0 },
  );
  const changeDateRange = async (dates: [Date | null, Date | null]) => {
    if (dates[0] == null || dates[1] == null) return;

    const endDate = new Date(dates[1]);
    endDate.setDate(endDate.getDate() + 1);
  };
  return (
    <>
      {(isSubmitting || isSubmitSuccessful) && !errorMessage && <Loading />}
      <div className={""}>
        {errorMessage && <span className={"text-red-500"}>{errorMessage}</span>}
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-1">
          <div className={"mb-2 flex flex-col gap-2"}>
            <label className="ml-1  text-xs font-bold text-slate-700 dark:text-white/80">
              Leistungszeitraum
            </label>
            <div className={"flex flex-row"}>
              <input
                {...register("startDate")}
                placeholder="Start Date"
                type="date"
                className="rounded border"
              />
              <input
                {...register("endDate")}
                placeholder="End Date"
                type="date"
                className="rounded border"
              />
            </div>
          </div>
          <div className={"flex flex-col sm:w-1/2"}>
            <label className="ml-1 text-xs font-bold text-slate-700 dark:text-white/80">
              Kunde/Lieferant
            </label>
            <select {...register("contactId")} placeholder="Contact ID" className="rounded border">
              {contacts.map((contact, index) => {
                return (
                  <option key={index} value={contact.id}>
                    {contact.name}
                  </option>
                );
              })}
            </select>
          </div>

          {fields.map((field, index) => (
            <div key={field.id} className="flex flex-col gap-2">
              <div className={"flex flex-row items-end gap-2"}>
                <div className={"w-1/12 max-w-full"}>
                  <label className="block">Pos</label>
                  <input
                    {...register(`invoicePositions.${index}.pos`)}
                    readOnly={true}
                    placeholder="Pos"
                    className="block w-full appearance-none rounded-lg border
             border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
                  />
                </div>
                <div className={"w-3/12 max-w-full"}>
                  <label className="block">Titel</label>
                  <input
                    {...register(`invoicePositions.${index}.title`)}
                    placeholder="Titel"
                    className="block w-full appearance-none rounded-lg border
             border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
                  />
                </div>
                <div className={"w-1/12 max-w-full"}>
                  <label className="block">Anzahl</label>
                  <input
                    {...register(`invoicePositions.${index}.amount`)}
                    placeholder="Amount"
                    className="block w-full appearance-none rounded-lg border
             border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
                  />
                </div>
                <div className={"w-1/12 max-w-full"}>
                  <label className="block">Einheit</label>
                  <input
                    {...register(`invoicePositions.${index}.unit`)}
                    placeholder="Unit"
                    className="block w-full appearance-none rounded-lg border
             border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
                  />
                </div>
                <div className={"w-1/12 max-w-full"}>
                  <label className="block">Einzelpreis</label>
                  <input
                    {...register(`invoicePositions.${index}.unitPrice`)}
                    placeholder="Einzelpreis"
                    type="number"
                    step={"0.001"}
                    className="block w-full appearance-none rounded-lg border
             border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
                  />
                </div>
                <div className={"w-1/12 max-w-full"}>
                  <label className="block">Ust.</label>
                  <input
                    {...register(`invoicePositions.${index}.taxRate`)}
                    placeholder="Ust."
                    type="number"
                    className="block w-full appearance-none rounded-lg border
             border-solid border-gray-300 bg-white bg-clip-padding
              px-3 py-2 text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
                  />
                </div>
                <div className={"w-1/12 max-w-full "}>
                  <label className="block flex justify-end text-right font-bold">Σ netto</label>
                  <input
                    disabled={true}
                    value={calcSum(index, false)}
                    className="block w-full appearance-none rounded-lg border border-solid
             border-gray-300 bg-white bg-clip-padding px-3
              py-2 text-right text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
                  />
                </div>
                <div className={"w-1/12 max-w-full "}>
                  <label className="block flex justify-end text-right font-bold">Σ brutto</label>
                  <input
                    disabled={true}
                    value={calcSum(index, true)}
                    className="block w-full appearance-none rounded-lg border border-solid
             border-gray-300 bg-white bg-clip-padding px-3
              py-2 text-right text-sm font-normal leading-5.6
               text-gray-700 outline-none transition-all ease-soft
                placeholder:text-gray-500  focus:shadow-soft-primary-outline
                 focus:outline-none dark:bg-gray-950 dark:text-white/80
                  dark:placeholder:text-white/80"
                  />
                </div>
                <div className={"flex w-1/12 max-w-full justify-end"}>
                  <Button
                    type={"button"}
                    onClick={() => {
                      remove(index);
                      const updatedFields = getValues("invoicePositions"); // Get the latest state of invoicePositions
                      updatedFields.forEach((field, fieldIndex) => {
                        setValue(`invoicePositions.${fieldIndex}.pos`, fieldIndex + 1); // Re-assign pos to remaining items
                      });
                    }}
                  >
                    <FaTrash />
                  </Button>
                </div>
              </div>
            </div>
          ))}
          <div className={"mt-5 flex w-full flex-row-reverse gap-1 font-bold"}>
            <span className={"mr-3 w-3/12 "}>Gesamtsumme (netto): {totalSumNet.toFixed(2)}€</span>
          </div>
          <div className={"flex w-full flex-row-reverse gap-1 font-bold"}>
            <span className={"mr-3 w-3/12 "}>
              Gesamtsumme (brutto): {totalSumGross.toFixed(2)}€
            </span>
          </div>
          <div className={"mt-5 flex flex-row justify-between"}>
            <div className={"flex flex-row gap-1"}>
              <Button
                type={"button"}
                onClick={() =>
                  append({
                    pos: fields.length + 1,
                    title: "",
                    unit: "",
                    amount: 0,
                    unitPrice: 0,
                    description: "",
                    taxRate: 19,
                  })
                }
              >
                <FaPlusCircle />
              </Button>
            </div>
            <Button>Erstellen</Button>
          </div>
        </form>
      </div>
    </>
  );
};
