"use client";
import React, { useState, useEffect, useCallback } from "react";
import { SystemNotification } from "~/component/top/NotificationDropdown";
import NotificationItem from "./NotificationItem";
import But<PERSON> from "~/component/button";
import { FaSync, FaFilter } from "react-icons/fa";
import { NotificationType } from "@prisma/client";

interface NotificationsListProps {}

const NotificationsList: React.FC<NotificationsListProps> = () => {
  const [notifications, setNotifications] = useState<SystemNotification[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [filter, setFilter] = useState<"all" | "unread">("all");
  const [typeFilter, setTypeFilter] = useState<NotificationType | "all">("all");
  const [unreadCount, setUnreadCount] = useState(0);

  const limit = 20;

  const fetchNotifications = useCallback(async (pageNum: number = 1, reset: boolean = false) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        page: pageNum.toString(),
        unreadOnly: filter === "unread" ? "true" : "false",
        ...(typeFilter !== "all" && { type: typeFilter }),
      });

      const response = await fetch(`/api/notifications/all?${params}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (reset || pageNum === 1) {
        setNotifications(data.notifications || []);
      } else {
        setNotifications(prev => [...prev, ...(data.notifications || [])]);
      }
      
      setUnreadCount(data.unreadCount || 0);
      setHasMore(data.hasMore || false);
    } catch (err) {
      console.error("Error fetching notifications:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch notifications");
    } finally {
      setLoading(false);
    }
  }, [filter, typeFilter, limit]);

  const markAsRead = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/notifications/${id}/read`, {
        method: "PATCH",
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, gelesen: true }
            : notification
        )
      );
      
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      console.error("Error marking notification as read:", err);
      setError(err instanceof Error ? err.message : "Failed to mark notification as read");
    }
  }, []);

  const markAllAsRead = useCallback(async () => {
    try {
      const response = await fetch("/api/notifications/mark-all-read", {
        method: "PATCH",
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, gelesen: true }))
      );
      
      setUnreadCount(0);
    } catch (err) {
      console.error("Error marking all notifications as read:", err);
      setError(err instanceof Error ? err.message : "Failed to mark all notifications as read");
    }
  }, []);

  const loadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchNotifications(nextPage, false);
    }
  };

  const handleFilterChange = (newFilter: "all" | "unread") => {
    setFilter(newFilter);
    setPage(1);
  };

  const handleTypeFilterChange = (newTypeFilter: NotificationType | "all") => {
    setTypeFilter(newTypeFilter);
    setPage(1);
  };

  const refreshNotifications = () => {
    setPage(1);
    fetchNotifications(1, true);
  };

  // Initial load and when filters change
  useEffect(() => {
    fetchNotifications(1, true);
  }, [fetchNotifications]);

  return (
    <div className="space-y-4">
      {/* Header with filters and actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold dark:text-white">
            Alle Benachrichtigungen
            {unreadCount > 0 && (
              <span className="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-red-500 rounded-full">
                {unreadCount}
              </span>
            )}
          </h2>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Filter buttons */}
          <div className="flex rounded-lg border border-gray-300 dark:border-gray-600">
            <button
              onClick={() => handleFilterChange("all")}
              className={`px-3 py-1 text-sm rounded-l-lg ${
                filter === "all" 
                  ? "bg-blue-500 text-white" 
                  : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              Alle
            </button>
            <button
              onClick={() => handleFilterChange("unread")}
              className={`px-3 py-1 text-sm rounded-r-lg border-l border-gray-300 dark:border-gray-600 ${
                filter === "unread" 
                  ? "bg-blue-500 text-white" 
                  : "bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              }`}
            >
              Ungelesen
            </button>
          </div>

          {/* Type filter */}
          <select
            value={typeFilter}
            onChange={(e) => handleTypeFilterChange(e.target.value as NotificationType | "all")}
            className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
          >
            <option value="all">Alle Typen</option>
            <option value={NotificationType.INFO}>Info</option>
            <option value={NotificationType.WARNING}>Warnung</option>
            <option value={NotificationType.ERROR}>Fehler</option>
            <option value={NotificationType.SUCCESS}>Erfolg</option>
          </select>

          {/* Action buttons */}
          <Button
            onClick={refreshNotifications}
            className="px-3 py-1 text-sm bg-gray-500 hover:bg-gray-600"
            disabled={loading}
          >
            <FaSync className={`${loading ? "animate-spin" : ""}`} />
          </Button>
          
          {unreadCount > 0 && (
            <Button
              onClick={markAllAsRead}
              className="px-3 py-1 text-sm bg-blue-500 hover:bg-blue-600"
            >
              Alle als gelesen
            </Button>
          )}
        </div>
      </div>

      {/* Error message */}
      {error && (
        <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <p className="text-red-700 dark:text-red-300">{error}</p>
        </div>
      )}

      {/* Notifications list */}
      <div className="space-y-2">
        {notifications.length === 0 && !loading ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {filter === "unread" ? "Keine ungelesenen Benachrichtigungen" : "Keine Benachrichtigungen"}
          </div>
        ) : (
          notifications.map((notification) => (
            <NotificationItem
              key={notification.id}
              notification={notification}
              onMarkAsRead={markAsRead}
            />
          ))
        )}
      </div>

      {/* Load more button */}
      {hasMore && notifications.length > 0 && (
        <div className="text-center pt-4">
          <Button
            onClick={loadMore}
            disabled={loading}
            className="px-6 py-2 bg-blue-500 hover:bg-blue-600"
          >
            {loading ? "Lädt..." : "Mehr laden"}
          </Button>
        </div>
      )}

      {/* Loading indicator */}
      {loading && notifications.length === 0 && (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Lädt Benachrichtigungen...</p>
        </div>
      )}
    </div>
  );
};

export default NotificationsList;
