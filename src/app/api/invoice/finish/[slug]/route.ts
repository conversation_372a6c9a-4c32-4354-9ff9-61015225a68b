import { NextRequest, NextResponse } from "next/server";

import prisma from "~/server/db/prisma";
import { createInvoicePDF, InvoiceWithIncludes } from "~/utils/invoice/createInvoicePDF";
import fs from "fs";
import { Cdr, CdrMapping, CdrPayout, KindOfInvoice, LogType, NotificationType, Role, StateOfInvoice } from "@prisma/client";
import Logger from "../../../../../server/logger/logger";
import crypto from "crypto";
import path from "path";
import { CREDIT_PREFIX, INVOICE_PREFIX } from "~/utils/invoice/const";
import { env } from "~/env.js";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { generateCSV } from "~/utils/csv/generateCSV";

interface Props {
  params: {
    slug: string;
  };
}

const createPDF = async (invoice: InvoiceWithIncludes, dir: string) => {
  try {
    return await createInvoicePDF({ invoice, dir });
  } catch (error: unknown) {
    Logger("Can`t generate Invoice", "Can`t generate Invoice", "invoice", LogType.ERROR);
  }
  return "";
};

export const createCdrCsv = async (invoice: InvoiceWithIncludes, dir: string) => {
  let fileName = `Cdr_for_invoice_${invoice.invoiceNumber}.csv`;
  if (invoice.kindOfInvoice === "CREDIT") {
    fileName = `Cdr_for_credit_${invoice.invoiceNumber}.csv`;
  }

  const filePath = path.join(dir, fileName);
  let csvData = "";
  if (invoice.kindOfInvoice === "CREDIT") {
    const cdrIds = invoice.creditCdrs.map((cdr) => `'${cdr.CDR_ID}'`).join(",");
    const cdrsWithPayoutData: (Cdr & CdrPayout)[] = await prisma.$queryRawUnsafe(
      `SELECT * from Cdr left join CdrPayout on CdR_ID = cdrId where CDR_ID in (${cdrIds})`,
    );
    const mapping = invoice?.contact?.cdrMapping ?? CdrMapping.Standard_Credit;
    csvData = generateCSV(cdrsWithPayoutData, mapping);
  }
  if (invoice.kindOfInvoice === "INVOICE") {
    const mapping = invoice?.contact?.cdrMapping ?? CdrMapping.Standard;
    csvData = generateCSV(invoice.cdrs, mapping);
  }

  fs.writeFileSync(filePath, csvData);

  return fileName;
};

export const getSha1 = (filePath: string): string => {
  const fileContent = fs.readFileSync(filePath);

  // Generate the SHA1 hash of the file content
  return crypto.createHash("sha1").update(fileContent).digest("hex");
};

export const checkAndCreateDir = (dir: string) => {
  // Überprüfen, ob Directory existiert, falls nicht, lege es an
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
  return;
};

export async function PUT(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.role || session?.user?.role != Role.ADMIN) {
    return new Response("No permission", { status: 400 });
  }

  if (!env.INVOICE_FOLDER) {
    return new Response("no invoice folder defined", { status: 500 });
  }

  const id = params.slug;

  const invoice = await prisma.invoice.findUnique({
    where: {
      id: id,
    },
    include: {
      contact: {
        include: {
          contactAddress: true,
        },
      },
      user: { include: { address: true } },
    },
  });

  if (!invoice) {
    return new Response("not found", { status: 500 });
  }

  if (invoice.invoiceId != null) {
    return new Response("invoice has invoiceId", { status: 500 });
  }
  const currentYear = new Date().getFullYear();
  // Start a transaction
  const updatedInvoice = await prisma.$transaction(async (prisma) => {
    const latest = await prisma.invoice.findFirst({
      where: {
        invoiceId: {
          not: null,
        },
        kindOfInvoice: invoice.kindOfInvoice,
      },
      orderBy: {
        invoiceId: "desc",
      },
    });

    let invoiceId = 1;
    if (latest && latest?.invoiceId) {
      invoiceId = latest?.invoiceId + 1;
    }

    let invoicePrefix = INVOICE_PREFIX;
    if (invoice.kindOfInvoice === "CREDIT") {
      invoicePrefix = CREDIT_PREFIX;
    }

    return prisma.invoice.update({
      where: {
        id: id,
      },
      data: {
        invoiceDate: new Date(),
        invoiceId: invoiceId,
        invoiceNumber: `${invoicePrefix}${currentYear}-${String(invoiceId).padStart(5, "0")}`,
        stateOfInvoice: StateOfInvoice.CREATED,
      },
      include: {
        invoicePositions: true,
        invoiceParent: true,
        contact: {
          include: {
            providers: true,
            contactAddress: true,
          },
        },
        user: { include: { address: true, ou: true } },
        cdrs: {
          where: {
            billable: {
              equals: true,
            },
          },
        },
        creditCdrs: true,
      },
    });
  });
  let dir = "";
  if (updatedInvoice.contact) {
    const provider_name =
      updatedInvoice?.contact?.name?.replaceAll(" ", "_").toLowerCase() ?? "unknown_contact_name";
    dir = `${env.INVOICE_FOLDER}/${currentYear}/${provider_name}`;
  } else if (updatedInvoice?.user) {
    const userOuName = updatedInvoice.user.ou.name;
    dir = `${env.INVOICE_FOLDER}/${currentYear}/${userOuName}/user`;
  }

  checkAndCreateDir(dir);

  const pdfPath = await createPDF(updatedInvoice, dir);

  let fileName = `Invoice_${updatedInvoice.invoiceNumber}.pdf`;
  if (updatedInvoice.kindOfInvoice === "CREDIT") {
    fileName = `Credit_${updatedInvoice.invoiceNumber}.pdf`;
  }

  await prisma.fileRef.create({
    data: {
      name: fileName,
      path: pdfPath,
      sha1: getSha1(pdfPath),
      invoiceId: updatedInvoice.id,
      contentType: "application/pdf",
    },
  });
  // create only csv file in case invoice is related to cdrs
  // e.g. CPO Invoices with monthly fees
  if (updatedInvoice?.creditCdrs?.length > 0 || updatedInvoice?.cdrs?.length > 0) {
    const cdrFileName = await createCdrCsv(updatedInvoice, dir);

    await prisma.fileRef.create({
      data: {
        name: cdrFileName,
        path: path.join(dir, cdrFileName),
        sha1: getSha1(path.join(dir, cdrFileName)),
        invoiceId: updatedInvoice.id,
        contentType: "text/csv",
      },
    });
  }

  // Send notification to user if this is a user invoice
  if (updatedInvoice.userId) {
    try {
      const totalGrossFormatted = updatedInvoice.sumGross.toFixed(2);
      const servicePeriodFrom = updatedInvoice.servicePeriodFrom?.toLocaleDateString('de-DE') || '';
      const servicePeriodTo = updatedInvoice.servicePeriodTo?.toLocaleDateString('de-DE') || '';

      await prisma.systemNotification.create({
        data: {
          nachricht: `Ihre Rechnung für den Zeitraum ${servicePeriodFrom} - ${servicePeriodTo} wurde abgeschlossen. Gesamtbetrag: ${totalGrossFormatted}€. Rechnungsnummer: ${updatedInvoice.invoiceNumber}`,
          type: NotificationType.SUCCESS,
          userId: updatedInvoice.userId,
        },
      });
    } catch (notificationError) {
      // Log error but don't fail the invoice finishing process
      console.error("Failed to create notification for finished user invoice:", notificationError);
    }
  }

  // Send notification to OU users if this is a credit invoice for a CPO contact
  if (updatedInvoice.kindOfInvoice === KindOfInvoice.CREDIT && updatedInvoice.contact?.cpo) {
    try {
      const totalGrossFormatted = Math.abs(updatedInvoice.sumGross).toFixed(2);
      const servicePeriodFrom = updatedInvoice.servicePeriodFrom?.toLocaleDateString('de-DE') || '';
      const servicePeriodTo = updatedInvoice.servicePeriodTo?.toLocaleDateString('de-DE') || '';

      // Find all CPO users in the OU that the contact belongs to
      if (updatedInvoice.contact.ouId) {
        const cpoUsers = await prisma.user.findMany({
          where: {
            ouId: updatedInvoice.contact.ouId,
            role: Role.CPO,
          },
          select: {
            id: true,
          },
        });

        // Create notifications for all CPO users in the OU
        const notificationPromises = cpoUsers.map(user =>
          prisma.systemNotification.create({
            data: {
              nachricht: `Eine Gutschrift für ${updatedInvoice.contact?.name || 'CPO'} wurde abgeschlossen. Zeitraum: ${servicePeriodFrom} - ${servicePeriodTo}. Betrag: ${totalGrossFormatted}€. Gutschrift-Nr: ${updatedInvoice.invoiceNumber}`,
              type: NotificationType.INFO,
              userId: user.id,
            },
          })
        );

        await Promise.all(notificationPromises);
      }
    } catch (notificationError) {
      // Log error but don't fail the invoice finishing process
      console.error("Failed to create notifications for credit invoice:", notificationError);
    }
  }

  return NextResponse.json({ invoiceNumber: updatedInvoice.invoiceNumber }, { status: 200 });
}
