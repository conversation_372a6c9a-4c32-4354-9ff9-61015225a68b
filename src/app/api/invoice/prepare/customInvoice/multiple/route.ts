import { NextRequest, NextResponse } from "next/server";

import prisma from "~/server/db/prisma";
import { type Invoice, Prisma, Role } from "@prisma/client";
import { getCPOInvoicePositions, prepareInvoiceByPositions } from "~/server/invoice/invoiceUtils";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return new Response("no auth", { status: 401 });
  }
  const { creditInvoiceIds } = await request.json();

  if (!creditInvoiceIds) {
    return new Response("no credit invoices id", { status: 500 });
  }

  const creditInvoiceObjects: Invoice[] = await prisma.invoice.findMany({
    where: { id: { in: creditInvoiceIds } },
  });
  try {
    const results = await Promise.allSettled(
      creditInvoiceObjects.map(async (creditInvoice: Invoice) => {
        const { positions: cpoInvoicePos, invoice: originalCreditInvoice } =
          await getCPOInvoicePositions(creditInvoice.id);
        if (creditInvoice?.invoiceDate && creditInvoice.contactId) {
          const startDateObj = new Date(
            creditInvoice.invoiceDate.getFullYear(),
            creditInvoice.invoiceDate.getMonth() - 1,
            1,
          );
          const endDateObj = new Date(
            creditInvoice.invoiceDate.getFullYear(),
            creditInvoice.invoiceDate.getMonth(),
            0,
          );

          // Berechnen der Gesamtsumme
          const { net: totalSumNet, gross: totalSumGross } = cpoInvoicePos.reduce(
            (acc: { net: number; gross: number }, pos) => {
              {
                acc.net += pos.unitPrice * pos.amount;
                if (pos.taxRate) {
                  acc.gross += pos.unitPrice * pos.amount * (1 + pos.taxRate / 100);
                } else {
                  acc.gross += pos.unitPrice * pos.amount;
                }

                return acc;
              }
            },
            { net: 0, gross: 0 },
          );

          const preparedInvoice = await prepareInvoiceByPositions({
            contactId: creditInvoice.contactId,
            invoicePositions: cpoInvoicePos,
            startDate: startDateObj,
            endDate: endDateObj,
            totalSumNet: totalSumNet,
            totalSumGross: totalSumGross,
          });
          return preparedInvoice;
        }
      }),
    );
    const successfulInvoices = results.filter(
      (result) =>
        result.status === "fulfilled" &&
        result.value && // Sicherstellen, dass value vorhanden ist
        "id" in result.value, // Prüfen, ob die id-Eigenschaft existiert
    );
    const errors = results.filter((result) => result.status === "rejected");

    if (successfulInvoices.length > 0) {
      return new Response(
        JSON.stringify({
          message: "Some or all CPO Invoices were created",
          successCount: successfulInvoices.length,
          errorCount: errors.length,
          errorMessages: `${errors
            .map((error) => ("reason" in error ? error.reason : ""))
            .join(",")}`,
        }),
        { status: 200, headers: { "Content-Type": "application/json" } },
      );
    } else {
      return new Response(
        JSON.stringify({
          message: "No CPO invoices were created.",
          errorCount: errors.length,
          errorMessages: `${errors
            .map((error) => ("reason" in error ? error.reason : ""))
            .join(",")}`,
        }),
        { status: 400, headers: { "Content-Type": "application/json" } },
      );
    }
  } catch (e) {
    if (e instanceof Error) {
      return NextResponse.json(e.message, { status: 500 });
    }
  }

  return new Response("no invoice", { status: 500 });
}
