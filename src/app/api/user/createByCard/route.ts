import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { hasher } from "~/server/hasher/hasher";

import { env } from "~/env.js";
import <PERSON><PERSON> from "stripe";
import { Role } from "@prisma/client";
import { sendVerifyEmailMail } from "~/utils/register/mails";
import crypto from "crypto";

const RegisterUserWithCardSchema = z.object({
  name: z.string(),
  lastName: z.string(),
  password: z.string().min(8),
  passwordRepeat: z.string().min(8),
  country: z.string(),
  postalCode: z.string(),
  city: z.string(),
  street: z.string(),
  houseNumber: z.string(),
  phone: z.string().optional(),
  email: z.string().email(),
  visualNumber: z.string(),
  registrationSlug: z.string(),
  selectedTarifIds: z.array(z.string()),
});

export async function POST(request: NextRequest) {
  const result = RegisterUserWithCardSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }

  const {
    name,
    email,
    lastName,
    password,
    country,
    postalCode,
    city,
    street,
    houseNumber,
    phone,
    visualNumber,
    registrationSlug,
    selectedTarifIds,
  } = result.data;

  try {
    const physicalCard = await prisma.physicalCard.findUnique({
      where: {
        visualNumber: visualNumber,
      },
      include: { EMPCard: true },
    });

    if (physicalCard && physicalCard?.EMPCard) {
      return NextResponse.json({ error: "Karte schon einem User zugeordnet" }, { status: 400 });
    }
    const contact = await prisma.contact.findUnique({
      where: { registrationSlug: registrationSlug },
    });

    if (contact?.ouId && physicalCard) {
      const user = await prisma.user.create({
        data: {
          name,
          lastName,
          email,
          ouId: contact?.ouId,
          phone,
          selectedOuId: contact?.ouId,
          password: await hasher(password),
          role: Role.CARD_HOLDER,
          signUpHash: crypto.randomBytes(32).toString("hex"),
        },
        include: { ou: true },
      });

      void (await prisma.contactAddress.create({
        data: {
          validFrom: new Date(),
          validTo: new Date(2099, 11, 31, 23, 59, 59, 999),
          street: street,
          streetNr: houseNumber,
          city: city,
          zip: postalCode,
          country: country,
          userId: user.id,
        },
      }));

      await prisma?.eMPCard.create({
        data: {
          userId: user.id,
          physicalCardId: physicalCard.uid,
          note: "Created with pre delivered card",
          tarifs: {
            create: selectedTarifIds.map((tid: string) => {
              return { tarif: { connect: { id: tid } } };
            }),
          },
        },
      });
      await sendVerifyEmailMail(user);

      return NextResponse.json({}, { status: 201 });
    }

    return NextResponse.json({ error: "Unknown error occurred." }, { status: 400 });
  } catch (e: unknown) {
    if (e instanceof Error) {
      return NextResponse.json({ error: e.message }, { status: 500 });
    }
    return NextResponse.json({ error: "Unknown error occurred." }, { status: 500 });
  }
}
