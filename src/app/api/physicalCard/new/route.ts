import type { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";

export async function POST(request: NextRequest) {
  const { uid, visualNumber, contactId, active } = await request.json();
  const session = await getServerSession(authOptions);

  if (!session || !uid || !visualNumber) {
    return new Response("no auth", { status: 401 });
  }
  try {
    const cardExists = await prisma.physicalCard.findUnique({
      where: { uid: uid },
    });
    if (cardExists) {
      return new Response("Card already exists - please check UID", { status: 422 });
    }
  } catch (e) {
    return new Response("Error checking existence", { status: 500 });
  }

  try {
    await prisma.physicalCard.create({
      data: { uid: uid, visualNumber: visualNumber, valid: active, cpoId: contactId },
    });
    return new Response("ok", { status: 200 });
  } catch (e) {
    return new Response("cant create", { status: 500 });
  }
}
