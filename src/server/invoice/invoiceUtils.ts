import prisma from "~/server/db/prisma";
import type { Cdr, CompanyTarif, Contact, Invoice, InvoicePosition, Prisma } from "@prisma/client";
import { Role } from "@prisma/client";
import { KindOfInvoice, LogType, StateOfInvoice } from "@prisma/client";
import type { ContactsWithOuAddressProviders } from "~/server/task/generateCredit";
import Logger from "~/server/logger/logger";
import { findMaxEndDatetime, findMinEndDatetime } from "~/utils/invoice/invoiceHelper";
import { ContactType, RangeMode } from "~/server/types/types";
import { getContactAdressByDate } from "~/utils/contact/getContactAdressByDate";
import type { CdrWithIncludes } from "~/server/invoice/calculateCost";
import { getFirstDayOfMonth, getLastDayOfLastMonth } from "~/utils/date/date";
import type { ClientInvoicePosition } from "~/app/(app)/invoice/component/CustomInvoiceForm";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { getPowerType, PowerType } from "~/utils/longship";
import type { InvoiceWithContactsWithAddress } from "~/app/(app)/invoice/[slug]/cpo/page";

export type InvoiceValues = {
  rangeMod: RangeMode;
  contactId: string;
  contactType: ContactType;
  dateRange: {
    0: Date;
    1: Date;
  };
};

const getContacts = () => {
  return prisma.contact.findMany({
    include: {
      providers: { include: { contact: true } },
      tarifs: { include: { tarif: true } },
      contactEnergyResellers: true,
    },
  });
};

export type ContactsWithTariffs = Prisma.ContactGetPayload<{
  include: {
    providers: { include: { contact: true } };
    tarifs: { include: { tarif: true } };
    contactEnergyResellers: true;
  };
}>;
export const getEMPLookupTable = async () => {
  const contacts = await getContacts();

  return contacts.reduce<{ [key: string]: ContactsWithTariffs }>((acc, contact) => {
    contact.providers.forEach((provider) => {
      const key = provider.providerCountryId + provider.providerId;
      acc[key] = contact;
    });
    return acc;
  }, {});
};

/*
export const getInvoicesForUser = async () => {
  const session = await getServerSession(authOptions);

  if (!session) {
    return false;
  }

  const id = session?.user.id;
  const invoices = await prisma.invoice.findMany({
    where: {
      userId: id,
    },
  });
  if (!invoices) {
    return false;
  }
  return invoices;
};
*/
export interface FindByProps {
  contact: ContactsWithOuAddressProviders;
  dateRange: {
    0: Date;
    1: Date;
  };
  rangeMod: RangeMode;
}

const findCdrBy = async ({ contact, dateRange, rangeMod }: FindByProps) => {
  const providerIds = contact.providers.map(
    (provider) => provider.providerCountryId + provider.providerId,
  );
  if (rangeMod === RangeMode.DATE_RANGE) {
    return prisma.cdr.findMany({
      where: {
        billable: true,
        End_datetime: {
          gte: new Date(dateRange[0]),
          lt: new Date(dateRange[1]),
        },
        Service_Provider_ID: {
          in: providerIds,
        },
        invoiceId: {
          equals: null,
        },
      },
      include: {
        cost: true,
        tarif: true,
      },
    });
  } else if (rangeMod === RangeMode.LAST_MONTH) {
    return prisma.cdr.findMany({
      where: {
        billable: true,
        End_datetime: {
          gte: new Date("01.01.2024"),
          lt: getLastDayOfLastMonth(),
        },
        Service_Provider_ID: {
          in: providerIds,
        },
        invoiceId: {
          equals: null,
        },
      },
      include: {
        cost: true,
        tarif: true,
      },
    });
  } else {
    return prisma.cdr.findMany({
      where: {
        billable: true,
        End_datetime: {
          gte: new Date("01.01.2024"),
          lt: new Date(),
        },
        Service_Provider_ID: {
          in: providerIds,
        },
        invoiceId: {
          equals: null,
        },
      },
      include: {
        cost: true,
        tarif: true,
      },
    });
  }
};

interface InvoiceProps {
  contact: Contact;
  minDate: Date;
  maxDate: Date;
  invoicePositions: InvoiceCollectionEntry[];
  cdrs: Cdr[];
  taxRate: number;
}
type InvoicePositionWithoutIds = Omit<InvoicePosition, "id" | "invoiceId">; // Erstellt einen neuen Typ ohne die 'E-Mail' Eigenschaft

const addInvoiceDataToDB = async ({
  contact,
  invoicePositions,
  minDate,
  maxDate,
  cdrs,
  taxRate,
}: InvoiceProps) => {
  const { totalKwh, totalSessions, serviceProvider } = invoicePositions.reduce(
    (acc, position) => {
      acc.totalKwh += position.kwh;
      acc.totalSessions += position.sessions;
      if (contact) {
        acc.serviceProvider.add(position?.contact ?? "");
      }
      return acc;
    },
    { totalKwh: 0, totalSessions: 0, serviceProvider: new Set() },
  );

  const positions: InvoicePositionWithoutIds[] = [];
  let posNumber = 0;
  const numServiceProvider = serviceProvider.size;
  for (const pos of invoicePositions) {
    const tarif = await prisma.tarif.findUnique({
      where: {
        id: pos.tarifId,
      },
    });
    if (!tarif) {
      return;
    }

    if (tarif.sessionFee) {
      posNumber += 1;
      const sumNet = +(tarif.sessionFee * pos.sessions).toFixed(2);
      positions.push({
        pos: posNumber,
        unitPrice: tarif.sessionFee,
        amount: pos.sessions,
        title: `SessionFee`,
        sumNet: sumNet,
        sumGross: +(sumNet * (taxRate / 100 + 1)).toFixed(2),
        sumTax: +(sumNet * (taxRate / 100)).toFixed(2),
        description: pos.description
          ? pos.description
          : `${tarif.name} ${numServiceProvider > 1 ? pos.contact : ""}`,
        unit: "session",
        tarifId: tarif.id,
        taxRate: taxRate,
      });
    }
    posNumber += 1;
    const sumNet = +(tarif.kwh * pos.kwh).toFixed(2);
    positions.push({
      pos: posNumber,
      unitPrice: tarif.kwh,
      amount: parseFloat(pos.kwh.toFixed(2)),
      title: `kWh Price`,
      description: pos.description
        ? pos.description
        : `${tarif.name} ${numServiceProvider > 1 ? pos.contact : ""}`,
      sumNet: sumNet,
      sumGross: +(sumNet * (taxRate / 100 + 1)).toFixed(2),
      sumTax: +(sumNet * (taxRate / 100)).toFixed(2),
      unit: "kWh",
      tarifId: tarif.id,
      taxRate: taxRate,
    });

    if (tarif.blockingFee > 0 && pos.blockingFee) {
      posNumber += 1;
      positions.push({
        pos: posNumber,
        unitPrice: tarif.blockingFee,
        amount: parseFloat(pos.blockingMin.toFixed(2)),
        title: `Blocking fee`,
        description: pos.description
          ? `${pos.description} (blocking fee)`
          : `${tarif.name} ${numServiceProvider > 1 ? pos.contact : ""}  (blocking fee)`,
        sumNet: pos.blockingFee,
        sumGross: +(pos.blockingFee * (taxRate / 100 + 1)).toFixed(2),
        sumTax: +(pos.blockingFee * (taxRate / 100)).toFixed(2),
        unit: "min",
        tarifId: tarif.id,
        taxRate: taxRate,
      });
    }
  }
  const invoiceData = {
    subject: `Roaming Invoice ${numServiceProvider == 1 ? cdrs[0]?.Service_Provider_ID : ""}`,
    kindOfInvoice: KindOfInvoice.INVOICE,
    stateOfInvoice: StateOfInvoice.PREVIEW,
    servicePeriodFrom: minDate,
    servicePeriodTo: maxDate,
    createDate: new Date(),
    invoiceDate: new Date(),
    sumKwh: totalKwh,
    sumSession: totalSessions,
    sumNet: positions.reduce((acc, curr) => acc + curr.sumNet, 0),
    sumGross: positions.reduce((acc, curr) => acc + curr.sumGross, 0),
    sumTax: positions.reduce((acc, curr) => acc + curr.sumTax, 0),
    contact: {
      connect: {
        id: contact.id,
      },
    },
    cdrs: {
      connect: cdrs.map((cdr) => ({
        CDR_ID: cdr.CDR_ID,
      })),
    },
    invoicePositions: {
      createMany: {
        data: positions,
      },
    },
  };

  const createdInvoice: Invoice = await prisma.invoice.create({
    data: invoiceData,
  });

  await prisma.cdr.updateMany({
    where: {
      CDR_ID: {
        in: cdrs.map((x) => x.CDR_ID),
      },
    },
    data: {
      invoiceId: createdInvoice.id,
    },
  });
  return createdInvoice;
};

export const prepareInvoiceByPositions = async ({
  contactId,
  invoicePositions,
  startDate,
  endDate,
  totalSumNet,
  totalSumGross,
  kindOfInvoice,
}: {
  contactId: string;
  invoicePositions: ClientInvoicePosition[];
  startDate: Date;
  endDate: Date;
  totalSumNet: number;
  totalSumGross: number;
  kindOfInvoice: KindOfInvoice;
}) => {
  const dbPositions = invoicePositions.map((pos) => {
    const sumNet = pos.amount * pos.unitPrice;
    const sumGross = sumNet * (pos.taxRate / 100 + 1);
    const sumTax = pos.taxRate ? sumGross - sumNet : 0;
    return {
      ...pos,
      description: pos.title,
      pos: +pos.pos,
      taxRate: +pos.taxRate,
      unitPrice: +pos.unitPrice,
      amount: +pos.amount,
      sumTax: sumTax,
      sumNet: sumNet,
      sumGross: sumGross,
    };
  });

  const invoiceData = {
    subject: kindOfInvoice === KindOfInvoice.CREDIT ? `Gutschrift` : `Rechnung`,
    kindOfInvoice: kindOfInvoice,
    stateOfInvoice: StateOfInvoice.PREVIEW,
    servicePeriodFrom: startDate,
    servicePeriodTo: endDate,
    createDate: new Date(),
    invoiceDate: new Date(),
    sumKwh: 0,
    sumSession: 0,
    sumNet: totalSumNet,
    sumGross: totalSumGross,
    sumTax: totalSumGross - totalSumNet,
    contact: {
      connect: {
        id: contactId,
      },
    },
    invoicePositions: {
      createMany: {
        data: dbPositions,
      },
    },
  };

  const createdInvoice: Invoice = await prisma.invoice.create({
    data: invoiceData,
  });
  return createdInvoice;
};

export const prepareInvoice = async ({
  contactId,
  contactType,
  dateRange,
  rangeMod,
}: InvoiceValues) => {
  const contact = await prisma.contact.findUnique({
    where: {
      id: contactId,
    },
    include: {
      providers: true,
      contactAddress: true,
      ou: true,
    },
  });
  if (!contact) {
    return false;
  }
  const cdrs = await findCdrBy({ contact, dateRange, rangeMod });

  if (cdrs.length == 0) {
    Logger("no cdrs found", "Generate Invoice", "invoice", LogType.WARN);
    throw new Error("no cdrs found");
  }

  const minDate = findMinEndDatetime(cdrs);
  const maxDate = findMaxEndDatetime(cdrs);

  if (!minDate || !maxDate) {
    return false;
  }

  let tokenGroup = null;
  if (contactType === ContactType.TOKEN_GROUP) {
    tokenGroup = await prisma.tokenGroup.findFirst({
      where: {
        contact: {
          id: contact.id,
        },
      },
      include: {
        tokens: true,
        billingOUs: true,
      },
    });
  }

  const invoicePositions = collectInvoiceData(cdrs, tokenGroup);

  const contactAddress = getContactAdressByDate(contact.contactAddress, minDate);
  const taxRate = contactAddress?.isNetInvoice ? 0 : contactAddress?.invoiceTaxRate || 0;

  return await addInvoiceDataToDB({
    contact,
    invoicePositions,
    minDate,
    maxDate,
    cdrs,
    taxRate,
  });
};

export type TokenGroupWithTokens = Prisma.TokenGroupGetPayload<{
  include: { tokens: true; billingOUs: true };
}>;
export type InvoiceCollectionEntry = {
  contact: string | undefined;
  Tariff_Name: string;
  tarifId?: string;
  kwh: number;
  sessions: number;
  price: number;
  invalidSessions: number;
  invalidKwh: number;
  energyCost: number;
  grossMargin: number;
  description: string;
  blockingFee: number;
  blockingMin: number;
};

export const collectInvoiceData = (
  cdrs: CdrWithIncludes[],
  tokenGroup: TokenGroupWithTokens | null = null,
): InvoiceCollectionEntry[] => {
  // Gruppieren der Daten nach contactId und Owner
  const tokens = tokenGroup?.tokens || [];

  const groupedData = cdrs.reduce((acc: { [key: string]: any }, cdr) => {
    if (!cdr.tarif) {
      return acc;
    }

    const tokenFromCdr = tokens.find((token) => token.authenticationId === cdr.Authentication_ID);

    const tarifId = cdr.tarif.id;
    let blockingMinutes = 0;
    if (cdr?.DurationInSec && cdr.tarif.blockingFeeBeginAtMin) {
      const deltaMin = cdr?.DurationInSec / 60 - cdr.tarif.blockingFeeBeginAtMin;
      if (deltaMin > 0) {
        blockingMinutes = deltaMin;
      }
    }

    const key = tokenFromCdr
      ? `${cdr.Service_Provider_ID}-${tarifId}-${tokenFromCdr.id}`
      : `${cdr.Service_Provider_ID}-${tarifId}`;

    if (!acc[key]) {
      acc[key] = {
        Volume: 0,
        sessions: 0,
        Calculated_Cost: 0,
        invalidSessions: 0,
        invalidKwh: 0,
        Tariff_Name: "",
        tarifId: null,
        EnergyCost: 0,
        GrossMargin: 0,
        kWhCost: 0,
        sessionCost: 0,
        blockingFee: 0,
        blockingMin: 0,
        description: "",
      };
    }

    if (!cdr.billable) {
      acc[key].invalidSessions++;
      acc[key].invalidKwh += cdr.Volume;
      acc[key].EnergyCost += cdr?.cost?.cost || 0;
    } else {
      acc[key].Volume += cdr.Volume;
      acc[key].sessions++;
      acc[key].Calculated_Cost += cdr.Calculated_Cost;
      acc[key].Tariff_Name = cdr.Tariff_Name;
      acc[key].tarifId = cdr.tarifId;
      acc[key].EnergyCost += cdr?.cost?.cost || 0;
      acc[key].blockingFee += cdr?.Parking_Time_Cost;
      acc[key].blockingMin += blockingMinutes;

      if (tokenFromCdr) {
        acc[key].description = `Card-Id: ${tokenFromCdr.name} ${
          tokenFromCdr.plateNumber && `/ ${tokenFromCdr.plateNumber}`
        } / Token: ${tokenFromCdr.authenticationId}`;
      }
    }
    return acc;
  }, {});

  // Konvertieren des gruppierten Datenobjekts in ein Array
  const invoicePositions = Object.entries(groupedData).map(([key, value]) => {
    return {
      contact: key.split("-")[0],
      Tariff_Name: value.Tariff_Name,
      tarifId: value.tarifId,
      kwh: value.Volume,
      sessions: value.sessions,
      price: value.Calculated_Cost,
      blockingFee: value.blockingFee,
      invalidSessions: value.invalidSessions,
      invalidKwh: value.invalidKwh,
      energyCost: value.EnergyCost,
      grossMargin: value.Calculated_Cost - value.EnergyCost,
      description: value.description,
      blockingMin: value.blockingMin,
    };
  });

  return invoicePositions;
};

interface UserInvoiceGroup {
  [key: string]: {
    energyCosts: number;
    kwh: number;
    sessionCosts: number;
    sessions: number;
    blockingMin: number;
    calculatedCosts: number;
    tarifId: string | null;
    blockingCosts: number;
    blockingFee: number; // angenommen, dass blockingFee null oder eine Zahl sein kann
    invalidSessions: number;
    invalidKwh: number;
    sessionPrice: number;
    tarif: CompanyTarif | null;
  };
}

export const prepareUserInvoice = async (userId: string, startDateStr: Date, endDateStr: Date) => {
  if (!userId) {
    Logger("No UserID", "Generate User Invoice", "invoice", LogType.ERROR);
    throw new Error("no userid provided");
  }
  const startDateObj = new Date(startDateStr);
  const endDateObj = new Date(endDateStr);
  endDateObj.setHours(23, 59, 59, 999);

  const taxRate = 19;
  const userCards = await prisma?.eMPCard.findMany({
    where: { userId: userId },
    include: {
      physicalCard: true,
      invoice: true,
      tarifs: { include: { tarif: { include: { ou: true } } } },
    },
  });

  const tokenUids = userCards
    .map((card) => card?.physicalCard?.uid)
    .filter((item): item is string => item !== undefined);
  const userCdrs = await prisma.cdr.findMany({
    where: {
      Authentication_ID: { in: tokenUids },
      invoiceId: null,
      AND: [
        {
          End_datetime: {
            gte: startDateObj,
          },
        },
        {
          End_datetime: {
            lte: endDateObj,
          },
        },
      ],
    },
  });

  if (userCdrs.length == 0) {
    Logger("no cdrs found", "Generate Invoice", "invoice", LogType.ERROR);
    throw new Error("no cdrs found");
  }

  const minDate = findMinEndDatetime(userCdrs);
  const maxDate = findMaxEndDatetime(userCdrs);

  if (!minDate || !maxDate) {
    return false;
  }
  const tariffs = userCards
    .flatMap((userCard) => userCard.tarifs.map((tarif) => tarif.tarif))
    .filter(
      (
        tarif,
      ): tarif is Prisma.CompanyTarifGetPayload<{
        include: { ou: true };
      }> => tarif !== undefined,
    );
  const groupedData = userCdrs.reduce((acc: UserInvoiceGroup, cdr) => {
    if (!cdr.companyTarifId) {
      return acc;
    }

    const tarif = tariffs.find((tarif) => tarif.id == cdr.companyTarifId);
    if (!tarif) {
      return acc;
    }
    if (!acc[cdr.companyTarifId]) {
      acc[cdr.companyTarifId] = {
        energyCosts: 0,
        kwh: 0,
        sessionCosts: 0,
        sessions: 0,
        blockingMin: 0,
        calculatedCosts: 0,
        blockingCosts: 0,
        invalidSessions: 0,
        invalidKwh: 0,
        blockingFee: 0,
        sessionPrice: 0,
        tarif: null,
        tarifId: null,
      };
    }
    const currentEntry = acc[cdr.companyTarifId];
    if (currentEntry) {
      if (!cdr.billable) {
        currentEntry.invalidSessions++;
        currentEntry.invalidKwh += cdr?.Volume || 0;
      } else {
        currentEntry.kwh += cdr.Volume || 0;
        currentEntry.sessionCosts += cdr.Start_Tariff || 0;
        currentEntry.sessions++;
        currentEntry.blockingCosts += cdr.Parking_Time_Cost || 0;
        currentEntry.energyCosts += cdr.EnergyCosts || 0;
        currentEntry.sessionPrice += tarif.sessionPrice;
        currentEntry.blockingFee += tarif.blockingFee;
        currentEntry.calculatedCosts += cdr.Calculated_Cost || 0;
        currentEntry.tarif = tarif;

        let blockingMinutes = 0;
        if (cdr?.DurationInSec && tarif.blockingFeeBeginAtMin) {
          const deltaMin = cdr.DurationInSec / 60 - tarif.blockingFeeBeginAtMin;
          if (deltaMin > 0) {
            blockingMinutes = deltaMin;
          }
        }
        currentEntry.blockingMin += blockingMinutes;
      }
    }
    return acc;
  }, {});
  const positions: InvoicePositionWithoutIds[] = [];

  let posNumber = 0;
  let totalKwh = 0;
  let totalSessions = 0;
  for (const [tarifId, calculatedItems] of Object.entries(groupedData)) {
    if (calculatedItems?.tarif?.sessionPrice) {
      posNumber += 1;
      const sumNet = calculatedItems.sessionCosts;

      positions.push({
        pos: posNumber,
        unitPrice: calculatedItems?.tarif?.sessionPrice,
        amount: calculatedItems.sessions,
        title: `SessionFee`,
        sumNet: sumNet,
        sumGross: +(sumNet * (taxRate / 100 + 1)).toFixed(2),
        sumTax: +(sumNet * (taxRate / 100)).toFixed(2),
        description: calculatedItems.tarif.name,
        unit: "session",
        taxRate: taxRate,
        tarifId: null,
      });
    }
    if (calculatedItems?.tarif?.energyPrice) {
      posNumber += 1;
      const sumNet = +calculatedItems.energyCosts.toFixed(2);
      positions.push({
        pos: posNumber,
        unitPrice: calculatedItems?.tarif?.energyPrice,
        amount: parseFloat(calculatedItems.kwh.toFixed(2)),
        title: `kWh Price`,
        description: calculatedItems.tarif.name,
        sumNet: sumNet,
        sumGross: +(sumNet * (taxRate / 100 + 1)).toFixed(2),
        sumTax: +(sumNet * (taxRate / 100)).toFixed(2),
        unit: "kWh",
        taxRate: taxRate,
        tarifId: null,
      });
    }
    if (calculatedItems?.tarif?.blockingFee && calculatedItems.blockingCosts) {
      posNumber += 1;
      positions.push({
        pos: posNumber,
        unitPrice: calculatedItems.tarif.blockingFee,
        amount: parseFloat(calculatedItems.blockingMin.toFixed(2)),
        title: `Blocking fee`,
        description: `${calculatedItems.tarif.name}  (blocking fee)`,
        sumNet: calculatedItems.blockingCosts,
        sumGross: +(calculatedItems.blockingCosts * (taxRate / 100 + 1)).toFixed(2),
        sumTax: +(calculatedItems.blockingCosts * (taxRate / 100)).toFixed(2),
        unit: "min",
        tarifId: null,
        taxRate: taxRate,
      });
    }
    totalKwh += calculatedItems.kwh;
    totalSessions += calculatedItems.sessions;
  }

  const unpaidCards = userCards.filter(
    (userCard) =>
      !userCard.invoice && userCard.tarifs[0]?.tarif.oneTimeFeePayer == Role.CARD_HOLDER,
  ); // auf multi tarife ändern
  if (unpaidCards.length > 0) {
    for (const unpaidCard of unpaidCards) {
      if (unpaidCard.tarifs[0]?.tarif.oneTimeFee) {
        posNumber += 1;
        positions.push({
          pos: posNumber,
          unitPrice: unpaidCard.tarifs[0]?.tarif.oneTimeFee,
          amount: parseFloat(unpaidCard.tarifs[0]?.tarif.oneTimeFee.toFixed(2)),
          title: `Kartenbestellgebühr (KartenNr. ${unpaidCard?.physicalCard?.visualNumber ?? ""})`,
          description: `Kartenbestellgebühr für Tarif ${unpaidCard.tarifs[0]?.tarif.name} (${unpaidCard?.physicalCard?.visualNumber}) `,
          sumNet: unpaidCard.tarifs[0]?.tarif.oneTimeFee,
          sumGross: +(unpaidCard.tarifs[0]?.tarif.oneTimeFee * (taxRate / 100 + 1)).toFixed(2),
          sumTax: +(unpaidCard.tarifs[0]?.tarif.oneTimeFee * (taxRate / 100)).toFixed(2),
          unit: "stk",
          tarifId: null,
          taxRate: taxRate,
        });
      }
    }
  }

  if (positions.length == 0) {
    Logger("No tarif mappend to CDRs", "Generate Invoice", "invoice", LogType.ERROR);
    throw new Error("No invoice positions generated. Have CDRs been calculated?");
  }
  const invoiceData = {
    subject: "Rechnung",
    kindOfInvoice: KindOfInvoice.INVOICE,
    stateOfInvoice: StateOfInvoice.PREVIEW,
    servicePeriodFrom: minDate,
    servicePeriodTo: maxDate,
    createDate: new Date(),
    invoiceDate: new Date(),
    sumKwh: totalKwh,
    sumSession: totalSessions,
    sumNet: positions.reduce((acc, curr) => acc + curr.sumNet, 0),
    sumGross: positions.reduce((acc, curr) => acc + curr.sumGross, 0),
    sumTax: positions.reduce((acc, curr) => acc + curr.sumTax, 0),
    user: {
      connect: {
        id: userId,
      },
    },
    cdrs: {
      connect: userCdrs.map((cdr) => ({
        CDR_ID: cdr.CDR_ID,
      })),
    },
    invoicePositions: {
      createMany: {
        data: positions,
      },
    },
  };

  const createdInvoice: Invoice = await prisma.invoice.create({
    data: invoiceData,
  });

  await prisma.cdr.updateMany({
    where: {
      CDR_ID: {
        in: userCdrs.map((x) => x.CDR_ID),
      },
    },
    data: {
      invoiceId: createdInvoice.id,
    },
  });
  return createdInvoice;
};

export interface ContractCalculations {
  kWhAC: number;
  kWhDC: number;
  costDirectPayment: number;
  costAdhoc: number;
  numSessions: number;
  numSessionsAC: number;
  numSessionsDC: number;
}

export const getCPOInvoicePositions = async (
  invoiceId: string,
): Promise<{
  positions: ClientInvoicePosition[];
  invoice: InvoiceWithContactsWithAddress | null;
}> => {
  const session = await getServerSession(authOptions);
  if (!session || !(session?.user?.role == Role.ADMIN)) {
    return { positions: [], invoice: null };
  }

  const startOfMonth = getFirstDayOfMonth();
  const endOfMonth = getLastDayOfLastMonth();
  const invoice = await prisma.invoice.findUnique({
    where: {
      id: invoiceId,
    },
    include: { contact: { include: { contactAddress: true } }, creditCdrs: true },
  });

  //todo exception
  const contract = await prisma.cPOContract.findFirstOrThrow({
    where: {
      invoices: {
        none: {
          invoiceDate: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      },
      contact: {
        id: invoice?.contactId ?? "",
      },
    },
    include: {
      invoices: true,
      contact: true,
    },
  });
  const initialSummary = {
    kWhAC: 0,
    kWhDC: 0,
    costDirectPayment: 0,
    costAdhoc: 0,
    numSessions: 0,
    numSessionsAC: 0,
    numSessionsDC: 0,
  };

  //basierend auf einer Gutschrift und dem aktuell gültigen CPO Contract wird eine monatliche Rechnung erstellt.
  // zunächst werder aus der Gutschrift die dynamischen abzurechnenden kosten berechnet
  // danach die Fixkosten wie Anzahl Ladepunkte, Hotline etc..
  const contractCalculations: ContractCalculations =
    invoice?.creditCdrs.reduce((summary: ContractCalculations, cdr) => {
      const powerType = getPowerType(cdr.Charge_Point_Type);

      // exclude CDRs which are not invoiced e.g. Company Tarif is internal and Calculated_Cost is 0€
      if (cdr?.Volume && cdr?.Calculated_Cost && cdr?.Calculated_Cost > 0) {
        if (powerType === PowerType.AC) {
          summary.kWhAC += cdr.Volume;
          summary.numSessionsAC++;
        } else if (powerType === PowerType.DC) {
          summary.kWhDC += cdr.Volume;
          summary.numSessionsDC++;
        }
        summary.numSessions++;
      }
      if (cdr?.Tariff_Name?.startsWith("Adhoc")) {
        summary.costAdhoc += cdr.Calculated_Cost ?? 0;
      }
      if (cdr?.Authentication_ID?.toUpperCase() == contract.directPaymentToken.toUpperCase()) {
        summary.costDirectPayment += cdr.Calculated_Cost ?? 0;
      }

      return summary;
    }, initialSummary) || initialSummary; //in case invoice is undefined, initialSummary is returned

  contractCalculations.kWhAC = Number(contractCalculations.kWhAC.toFixed(2));
  contractCalculations.kWhDC = Number(contractCalculations.kWhDC.toFixed(2));

  const positions = [];
  let positionCounter = 1;

  if (contract.priceACCharger > 0 && contract.numACCharger > 0) {
    positions.push({
      pos: positionCounter,
      title: "AC Ladepunkte",
      unit: "Stk",
      unitPrice: contract.priceACCharger,
      amount: contract.numACCharger,
      description: "monatliche Gebühr für AC Ladepunkte",
      taxRate: 19,
    });
    positionCounter++;
  }

  if (contract.priceDCCharger > 0 && contract.numDCCharger > 0) {
    positions.push({
      pos: positionCounter,
      title: "DC Ladepunkte",
      unit: "Stk",
      unitPrice: contract.priceDCCharger,
      amount: contract.numDCCharger,
      description: "monatliche Gebühr für DC Ladepunkte",
      taxRate: 19,
    });
    positionCounter++;
  }
  if (contract.priceACHotline > 0 && contract.numACHotline > 0) {
    positions.push({
      pos: positionCounter,
      title: "Hotline AC Ladepunkte",
      unit: "Stk",
      unitPrice: contract.priceACHotline,
      amount: contract.numACHotline,
      description: "monatliche Gebühr für Hotline AC Ladepunkte",
      taxRate: 19,
    });
    positionCounter++;
  }

  if (contract.priceDCHotline > 0 && contract.numDCHotline) {
    positions.push({
      pos: positionCounter,
      title: "Hotline DC Ladepunkte",
      unit: "Stk",
      unitPrice: contract.priceDCHotline,
      amount: contract.numDCHotline,
      description: "monatliche Gebühr für Hotline DC Ladepunkte",
      taxRate: 19,
    });
    positionCounter++;
  }

  // Verwende die neuen AC/DC-spezifischen Felder, wenn vorhanden, sonst das Legacy-Feld
  if (contractCalculations.kWhAC > 0) {
    const acFee = contract.kwhFeeCentAC > 0 ? contract.kwhFeeCentAC : contract.kwhFeeCent;
    if (acFee > 0) {
      positions.push({
        pos: positionCounter,
        title: "Transaktionsgebühr AC",
        unit: "kWh",
        unitPrice: acFee / 100,
        amount: contractCalculations.kWhAC,
        description: "Transaktionsgebühr AC",
        taxRate: 19,
      });
      positionCounter++;
    }
  }

  if (contractCalculations.kWhDC > 0) {
    const dcFee = contract.kwhFeeCentDC > 0 ? contract.kwhFeeCentDC : contract.kwhFeeCent;
    if (dcFee > 0) {
      positions.push({
        pos: positionCounter,
        title: "Transaktionsgebühr DC",
        unit: "kWh",
        unitPrice: dcFee / 100,
        amount: contractCalculations.kWhDC,
        description: "Transaktionsgebühr DC",
        taxRate: 19,
      });
      positionCounter++;
    }
  }

  // Verwende die neuen AC/DC-spezifischen Session-Felder, wenn vorhanden
  if (contract.sessionFeeCentAC > 0 && contractCalculations.numSessionsAC > 0) {
    positions.push({
      pos: positionCounter,
      title: "Sessiongebühr AC",
      unit: "session",
      unitPrice: contract.sessionFeeCentAC / 100,
      amount: contractCalculations.numSessionsAC,
      description: "Sessiongebühr AC",
      taxRate: 19,
    });
    positionCounter++;
  }

  if (contract.sessionFeeCentDC > 0 && contractCalculations.numSessionsDC > 0) {
    positions.push({
      pos: positionCounter,
      title: "Sessiongebühr DC",
      unit: "session",
      unitPrice: contract.sessionFeeCentDC / 100,
      amount: contractCalculations.numSessionsDC,
      description: "Sessiongebühr DC",
      taxRate: 19,
    });
    positionCounter++;
  }

  // Fallback auf das Legacy-Feld, wenn keine AC/DC-spezifischen Felder gesetzt sind
  if (contract.sessionFeeCentAC > 0 && contractCalculations.numSessionsAC > 0) {
    positions.push({
      pos: positionCounter,
      title: "Sessiongebühr AC",
      unit: "session",
      unitPrice: contract.sessionFeeCentAC / 100,
      amount: contractCalculations.numSessionsAC,
      description: "Sessiongebühr AC",
      taxRate: 19,
    });
    positionCounter++;
  }
  if (contract.sessionFeeCentDC > 0 && contractCalculations.numSessionsDC > 0) {
    positions.push({
      pos: positionCounter,
      title: "Sessiongebühr DC",
      unit: "session",
      unitPrice: contract.sessionFeeCentDC / 100,
      amount: contractCalculations.numSessionsDC,
      description: "Sessiongebühr DC",
      taxRate: 19,
    });
  }

  if (contract.serviceFeePerAC > 0 && contract.numACCharger > 0) {
    positions.push({
      pos: positionCounter,
      title: "Servicegebühr AC Ladepunkt",
      unit: "Stk",
      unitPrice: contract.serviceFeePerAC,
      amount: contract.numACCharger,
      description: "Servicegebühr AC Ladepunkt",
      taxRate: 19,
    });
    positionCounter++;
  }

  if (contract.serviceFeePerDC > 0 && contract.numDCCharger > 0) {
    positions.push({
      pos: positionCounter,
      title: "Servicegebühr DC Ladepunkt",
      unit: "Stk",
      unitPrice: contract.serviceFeePerDC,
      amount: contract.numDCCharger,
      description: "Servicegebühr DC Ladepunkt",
      taxRate: 19,
    });
    positionCounter++;
  }
  if (contractCalculations.costAdhoc > 0 && contract.adhocPaymentFeePercent > 0) {
    positions.push({
      pos: positionCounter,
      title: `Transaktionsgebühr Adhoc (${
        contract.adhocPaymentFeePercent
      }% von ${contractCalculations.costAdhoc.toFixed(2)}€)`,
      unit: "Stk",
      unitPrice: Number(
        (contractCalculations.costAdhoc * (contract.adhocPaymentFeePercent / 100)).toFixed(2),
      ),
      amount: 1,
      description: "Transaktionsgebühr Adhoc",
      taxRate: 19,
    });
    positionCounter++;
  }

  if (contractCalculations.costDirectPayment > 0 && contract.directPaymentFeePercent > 0) {
    positions.push({
      pos: positionCounter,
      title: `Transaktionsgebühr Direct Payment (${
        contract.directPaymentFeePercent
      }% von ${contractCalculations.costDirectPayment.toFixed(2)}€)`,
      unit: "Stk",
      unitPrice: Number(
        (contractCalculations.costDirectPayment * (contract.directPaymentFeePercent / 100)).toFixed(
          2,
        ),
      ),
      amount: 1,
      description: "Transaktionsgebühr Direct Payment",
      taxRate: 19,
    });
    positionCounter++;
  }

  // Mitarbeiterladen/Clubladen Gebühr hinzufügen, wenn vorhanden
  if (contract.monthlyEmployeeClubCharging > 0) {
    positions.push({
      pos: positionCounter,
      title: "Mitarbeiterladen/Clubladen Gebühr",
      unit: "Monat",
      unitPrice: contract.monthlyEmployeeClubCharging,
      amount: 1,
      description: "Monatliche Gebühr für Mitarbeiterladen/Clubladen",
      taxRate: 19,
    });
    positionCounter++;
  }

  return { positions: positions, invoice: invoice };
};
