import prisma from "~/server/db/prisma";
import Lo<PERSON> from "~/server/logger/logger";
import type { FileRef } from "@prisma/client";
import { KindOfInvoice, LogType, StateOfInvoice } from "@prisma/client";
import { promises as fs } from "fs";
import { env } from "~/env.js";
import { mapMultipleInvoicesToTransaction } from "~/app/api/qonto/mapInvoices/route";

export const uploadQontoAttachement = async (fileRef: FileRef, url: string) => {
  const formData = new FormData();
  const data = await fs.readFile(fileRef.path);
  const file = new Blob([data], { type: "application/octet-stream" });
  formData.append("file", file, fileRef.name);

  const options = {
    method: "POST",
    headers: {
      "X-Qonto-Idempotency-Key": fileRef.name,
      Accept: "application/json",
      Authorization: `${env.QONTO_LOGIN}:${env.QONTO_SECRET}`,
    },
    body: formData,
  };

  try {
    const response = await fetch(url, options);
    const data = await response.json();
    if (response.ok) {
      //ersteller hier system notification
    }
    Logger(JSON.stringify(data), "Antwort von Qonto API", "FileUpload", LogType.INFO);
  } catch (error) {
    const errorMsg = (error as Error).message;
    Logger(errorMsg, "Fehler beim Hochladen der Datei", "FileUpload", LogType.ERROR);
  }
};
export const uploadInvoiceToTransaction = async (invoiceId: string, transaction_id: string) => {
  if (env.DEBUG == "true" || env.DEBUG == "1") {
    console.log("Debug detected - do not upload");
    return;
  }

  const fileRef = await prisma.fileRef.findFirst({
    where: {
      invoiceId: invoiceId,
    },
  });

  if (!fileRef) {
    Logger(
      `Keine Datei gefunden für invoiceId: ${invoiceId}`,
      "Fehler beim Hochladen der Datei",
      "FileUpload",
      LogType.ERROR,
    );
    return;
  }

  const qonto_id_for_transaction_obj = await prisma.qontoTransaction.findFirst({
    where: { transaction_id: transaction_id },
    select: { id: true },
  });
  if (!qonto_id_for_transaction_obj) {
    Logger(
      `Qonto ID for transaction_id: ${transaction_id} not found`,
      "Fehler beim Hochladen der Datei",
      "FileUpload",
      LogType.ERROR,
    );
    return;
  }

  const url = `https://thirdparty.qonto.com/v2/transactions/${qonto_id_for_transaction_obj.id}/attachments`;
  Logger(`URL for Auto upload: ${url}`, "Upload URL", "FileUpload", LogType.ERROR);
  return await uploadQontoAttachement(fileRef, url);
};

export const autoUploadInvoices = async (gutschriften = false) => {
  //if (env.DEBUG) {
  //  return;
  //}
  const unmappedTransactions = await prisma.qontoTransaction.findMany({
    where: {
      NOT: [
        { InvoiceToQontoTransaction: { some: {} } },
        { StripePayoutToQontoTransaction: { some: {} } },
      ],
      ignore_for_mapping: false,
      side: gutschriften ? "debit" : "credit",
    },
  });

  const unpaidInvoices = await prisma.invoice.findMany({
    where: {
      paidOnDate: null,
      kindOfInvoice: gutschriften ? KindOfInvoice.CREDIT : KindOfInvoice.INVOICE,
      stateOfInvoice: {
        not: StateOfInvoice.CANCEL,
      },
    },
    include: { contact: true },
  });

  const TOLERANCE = 0.05;

  for (const transaction of unmappedTransactions) {
    // Finde alle Rechnungen, die zur aktuellen Transaktion basierend auf dem local_amount und der Toleranz passen
    let matchingInvoices = unpaidInvoices.filter(
      (invoice) => Math.abs(invoice.sumGross - transaction.local_amount) <= TOLERANCE,
    );
    if (matchingInvoices.length === 1) {
      const invoiceNumber = matchingInvoices[0]?.invoiceNumber;
      const invoiceId = matchingInvoices[0]?.id;
      const contactIban = matchingInvoices[0]?.contact?.iban;
      if (invoiceNumber) {
        let check = transaction?.reference?.includes(invoiceNumber);
        if (!check) {
          check = transaction?.reference?.includes(invoiceNumber.replaceAll("-", ""));
        }
        if (!check && contactIban) {
          check = transaction?.iban?.replaceAll(" ", "") == contactIban.replaceAll(" ", "");
        }
        if (check && invoiceId) {
          const successResponse = await mapMultipleInvoicesToTransaction(
            [invoiceId],
            transaction.transaction_id,
          );
          if (successResponse.ok) {
            //const success = uploadInvoiceToTransaction(invoiceId, transaction.transaction_id);

            Logger(
              `Autoupload ${invoiceId}  ${transaction.label}  ${transaction.transaction_id}`,
              "Auto upload log check",
              "Finance",
              LogType.INFO,
            );
          } else {
            //const success = uploadInvoiceToTransaction(invoiceId, transaction.transaction_id);
            Logger(
              `${await successResponse.text()} Autoupload ${invoiceId}  ${transaction.label}  ${
                transaction.transaction_id
              }`,
              "Auto upload failed",
              "Finance",
              LogType.ERROR,
            );
          }

          //prisma.in;
          return; // entspricht continue in diesem fall
        }
      }
    }

    // Falls keine passenden Rechnungen basierend auf dem Betrag gefunden wurden, prüfe die invoiceNumber in der reference
    if (matchingInvoices.length === 0) {
      const referenceMatchingInvoices = unpaidInvoices.filter((invoice) =>
        transaction.reference && invoice.invoiceNumber
          ? transaction.reference.includes(invoice?.invoiceNumber)
          : false,
      );

      // Wenn mehrere Rechnungen über die Referenz gefunden wurden, prüfe, ob ihre Summe (mit Toleranz) dem local_amount entspricht
      const sumOfReferenceMatchingInvoices = referenceMatchingInvoices.reduce(
        (sum, invoice) => sum + invoice.sumGross,
        0,
      );

      if (Math.abs(sumOfReferenceMatchingInvoices - transaction.local_amount) <= TOLERANCE) {
        matchingInvoices = referenceMatchingInvoices;
      }

      if (matchingInvoices && matchingInvoices.length > 0) {
        const matchingIds = matchingInvoices.map((invoice) => invoice.id);
        if (matchingIds) {
          Logger(
            `Autoupload multiple ${matchingIds}  ${transaction.label}  ${transaction.transaction_id}`,
            "Auto upload log check",
            "Finance",
            LogType.INFO,
          );
          const successResponse = await mapMultipleInvoicesToTransaction(
            matchingIds,
            transaction.transaction_id,
          );
          if (successResponse.ok) {
            //const success = uploadInvoiceToTransaction(invoiceId, transaction.transaction_id);
            Logger(
              `Autoupload ${matchingIds}  ${transaction.label}  ${transaction.transaction_id}`,
              "Auto upload log check",
              "Finance",
              LogType.INFO,
            );
          } else {
            //const success = uploadInvoiceToTransaction(invoiceId, transaction.transaction_id);
            Logger(
              `${await successResponse.text()} Autoupload ${matchingIds}  ${transaction.label}  ${
                transaction.transaction_id
              }`,
              "Auto upload failed",
              "Finance",
              LogType.ERROR,
            );
          }
        }
      }
    }
  }
};
