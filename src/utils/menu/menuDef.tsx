import React from "react";
import { Role } from "@prisma/client";
import {
  Md<PERSON><PERSON>untBalance,
  MdEditLocationAlt,
  MdElectricCar,
  MdOutlineQueryStats,
  MdPriceCheck,
  MdPayments,
} from "react-icons/md";
import {
  FaAddressBook,
  FaBell,
  FaChartLine,
  FaFileInvoiceDollar,
  FaHome,
  FaStripeS,
  FaUsers,
} from "react-icons/fa";
import { TbClockShare } from "react-icons/tb";
import { BsDatabaseFillGear } from "react-icons/bs";
import { GiKeyCard } from "react-icons/gi";
import { IoLogoBuffer } from "react-icons/io";
import { BiRfid } from "react-icons/bi";
import {
  IoCashOutline,
  IoHelpCircleOutline,
  IoPricetagsOutline,
  IoReceiptOutline,
} from "react-icons/io5";
import { <PERSON><PERSON>utlineLineChart, AiOutlineUser } from "react-icons/ai";
import { RiParkingBoxLine } from "react-icons/ri";
import { SiAwsorganizations, SiSepa } from "react-icons/si";
import { HiCommandLine } from "react-icons/hi2";
import { LiaFileContractSolid } from "react-icons/lia";

export enum MenuType {
  divider,
  menu,
}

export interface MenuEntry {
  name: string;
  type: MenuType.menu;
  href: string;
  subMenu?: MenuEntry[];
  icon: React.ReactElement;
  role: Role[];
}

export interface MenuDivider {
  name: string;
  type: MenuType.divider;
  role: Role[];
  href?: string;
}

export type MenuItem = MenuEntry | MenuDivider;

export const menuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
    role: [Role.USER, Role.ADMIN, Role.CPO],
  },
  {
    name: "Realtime",
    type: MenuType.menu,
    href: "/",
    icon: <MdOutlineQueryStats size={22} />,
    role: [Role.USER, Role.ADMIN, Role.CARD_MANAGER, Role.CPO],
  },
  {
    name: "Home",
    type: MenuType.menu,
    href: "/",
    icon: <FaHome size={22} />,
    role: [Role.CARD_HOLDER],
  },
  {
    name: "History",
    type: MenuType.menu,
    href: "/history",
    icon: <AiOutlineLineChart size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Parking",
    type: MenuType.menu,
    href: "/parkingsensor",
    icon: <RiParkingBoxLine size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Financial",
    type: MenuType.divider,
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Forecast",
    href: "/financal-forecast",
    type: MenuType.menu,
    icon: <FaChartLine size={22} />,
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Invoice & Credit",
    href: "/invoice",
    type: MenuType.menu,
    icon: <FaFileInvoiceDollar size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Invoice forecast",
    href: "/invoice-forecast",
    type: MenuType.menu,
    icon: <TbClockShare size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Qonto Transactions",
    href: "/qonto-transactions",
    type: MenuType.menu,
    icon: <MdAccountBalance size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Stripe",
    href: "/stripe",
    type: MenuType.menu,
    icon: <FaStripeS size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "CPO Verträge",
    href: "/cpoContract",
    type: MenuType.menu,
    icon: <LiaFileContractSolid size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Adhoc-Tarif",
    href: "/adhoc-tarif",
    type: MenuType.menu,
    icon: <MdPayments size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Data",
    type: MenuType.divider,
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "CDRs",
    href: "/cdr",
    type: MenuType.menu,
    icon: <BsDatabaseFillGear size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "EMP & CPO",
    href: "/contact",
    type: MenuType.menu,
    icon: <FaAddressBook size={22} />,
    role: [Role.ADMIN],
  },

  {
    name: "Flottenkarten (Token)",
    href: "/tokenGroup",
    type: MenuType.menu,
    icon: <GiKeyCard size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Tarife",
    href: "/tarif",
    type: MenuType.menu,
    icon: <MdPriceCheck size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Locations",
    type: MenuType.menu,
    href: "/location",
    icon: <MdEditLocationAlt size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Admin",
    type: MenuType.divider,
    role: [Role.ADMIN],
  },
  {
    name: "Tenant",
    type: MenuType.menu,
    href: "/tenantconfiguration",
    icon: <SiAwsorganizations size={30} />,
    role: [Role.ADMIN],
  },
  {
    name: "Benutzerverwaltung",
    type: MenuType.menu,
    href: "/users",
    icon: <FaUsers size={30} />,
    role: [Role.ADMIN],
  },
  {
    name: "Command",
    type: MenuType.menu,
    role: [Role.ADMIN],
    icon: <HiCommandLine size={22} />,
    href: "/command",
  },
  {
    name: "Log",
    type: MenuType.menu,
    href: "/log",
    icon: <IoLogoBuffer size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
    role: [Role.ADMIN],
  },

  {
    name: "Zahlungsdaten",
    type: MenuType.menu,
    href: "/emp/payment",
    icon: <IoCashOutline size={22} />,
    role: [Role.CARD_HOLDER],
  },
  {
    name: "Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: <BiRfid size={22} />,
    role: [Role.CARD_HOLDER, Role.CARD_MANAGER],
  },
  {
    name: "Mitarbeiter-Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: <BiRfid size={22} />,
    role: [Role.ADMIN, Role.USER],
  },
  {
    name: "Mein Tarif",
    type: MenuType.menu,
    href: "/emp/tarif/userview",
    icon: <IoPricetagsOutline size={22} />,
    role: [Role.CARD_HOLDER],
  },
  {
    name: "Firmentarife",
    type: MenuType.menu,
    href: "/emp/tarif/managerview",
    icon: <IoPricetagsOutline size={22} />,
    role: [Role.CARD_MANAGER],
  },
  {
    name: "Firmentarife Ou",
    type: MenuType.menu,
    href: "/emp/tarif/managerview",
    icon: <IoPricetagsOutline size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Ladevorgänge Ou",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: <IoLogoBuffer size={22} />,
    role: [Role.ADMIN],
  },
  {
    name: "Ladevorgänge",
    type: MenuType.menu,
    href: "/emp/charging-history/userview",
    icon: <IoLogoBuffer size={22} />,
    role: [Role.CARD_HOLDER],
  },
  {
    name: "Ladevorgänge",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: <IoLogoBuffer size={22} />,
    role: [Role.CARD_MANAGER, Role.CPO],
  },

  {
    name: "Rechnungen",
    type: MenuType.menu,
    href: "/emp/invoice/userview",
    icon: <IoReceiptOutline size={22} />,
    role: [Role.CARD_HOLDER],
  },

  {
    name: "Rechnungen",
    type: MenuType.menu,
    href: "/emp/invoice/managerview",
    icon: <IoReceiptOutline size={22} />,
    role: [Role.CARD_MANAGER],
  },

  {
    name: "Benutzerverwaltung",
    type: MenuType.menu,
    href: "/users",
    icon: <FaUsers size={30} />,
    role: [Role.CARD_MANAGER],
  },

  {
    name: "Sonstiges",
    type: MenuType.divider,
    role: [Role.ADMIN, Role.CPO],
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: <FaBell size={22} />,
    role: [Role.ADMIN, Role.CARD_HOLDER, Role.CARD_MANAGER, Role.CPO, Role.USER],
  },
  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: <AiOutlineUser size={22} />,
    role: [Role.ADMIN, Role.CARD_HOLDER, Role.CARD_MANAGER, Role.CPO],
  },

  {
    name: "Support",
    type: MenuType.menu,
    href: "/emp/support",
    icon: <IoHelpCircleOutline size={22} />,
    role: [Role.ADMIN, Role.CARD_HOLDER, Role.CARD_MANAGER, Role.CPO],
  },
];
