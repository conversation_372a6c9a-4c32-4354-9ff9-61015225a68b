import fs from "fs";
import { PDFName, PDFDocument as plibPDFDocument } from "pdf-lib";
import PDFDocument from "pdfkit";
import type { Prisma } from "@prisma/client";
import { getContactAdressByDate } from "~/utils/contact/getContactAdressByDate";
import { KindOfInvoice } from "@prisma/client";
import { DateTime } from "luxon";
import { createFinalZUGFeRDXML } from "~/utils/invoice/ZUGFeRD";
import { execSync } from "child_process";

export type InvoiceWithIncludes = Prisma.InvoiceGetPayload<{
  include: {
    contact: {
      include: {
        providers: true;
        contactAddress: true;
      };
    };
    user: { include: { address: true } };
    cdrs: true;
    creditCdrs: true;
    invoicePositions: true;
    invoiceParent: true;
  };
}>;

interface createInvoiceProps {
  invoice: InvoiceWithIncludes;
  dir: string;
}

const padTo2Digits = (num: number) => {
  return num.toString().padStart(2, "0");
};

const formatDate = (date: Date) => {
  return [padTo2Digits(date.getDate()), padTo2Digits(date.getMonth() + 1), date.getFullYear()].join(
    ".",
  );
};

function drawTableHeader(doc: typeof PDFDocument, y: number) {
  doc.y = y;
  doc.rect(48, y, 517, 18).fillOpacity(0.25).fill("grey");
  doc.y += 2;
  doc.fillColor("black").fillOpacity(1);
  doc.font("public/OpenSans/OpenSans-Bold.ttf");
  doc.text("Pos.", 50, doc.y, {});
  doc.moveUp();
  doc.text("Beschreibung/Tarif", 80, doc.y, {});
  doc.moveUp();
  doc.text("Menge", 290, doc.y, {});
  doc.moveUp();
  doc.text("Einheit", 350, doc.y, {});
  doc.moveUp();
  doc.text("MwSt.", 390, doc.y, {});
  doc.moveUp();
  doc.text("Einzelpreis", 435, doc.y, { width: 55 });
  doc.moveUp();
  doc.text("Gesamtpreis", 499, doc.y, { lineBreak: false });
}

export function drawFooterAndLogo(doc: typeof PDFDocument, onlyFooter = false, xOffset = 0) {
  // Füge Logo hinzu
  if (onlyFooter == false) {
    doc.image("public/logo/EULEKTRO_21697c_R33_G105_B124.png", 36, 0, {
      height: 110,
    });
  }

  // create Footer
  doc
    .moveTo(50 + xOffset, 785)
    .lineTo(560 - xOffset, 785)
    .lineWidth(0.5)
    .strokeColor("grey")
    .stroke();
  doc.fillColor("black").fillOpacity(1);
  doc.moveDown();
  doc.font("public/OpenSans/OpenSans-Bold.ttf").fontSize(6);
  doc.text("Eulektro GmbH", 50 + xOffset, 790, { lineBreak: false });
  doc.text("Kontakt", 200 + xOffset, 790, { lineBreak: false });
  doc.text("Bankverbindung", 350 + xOffset, 790, { lineBreak: false });
  doc.font("public/OpenSans/OpenSans-Regular.ttf");

  doc.moveDown();
  doc.text("Werderstraße 69", 50 + xOffset, 798, { lineBreak: false });
  doc.text("28199 Bremen", 50 + xOffset, 806, { lineBreak: false });
  doc.text("USt-ID: DE343815692", 50 + xOffset, 814, { lineBreak: false });
  doc.text("Geschäftsführer: Jan Runkel", 50 + xOffset, 822, { lineBreak: false });
  doc.text("Handelsregisternr.: HRB 36822 HB", 50 + xOffset, 830, { lineBreak: false });
  doc.text("E-Mail: <EMAIL>", 200 + xOffset, 798, { lineBreak: false });
  doc.text("Website: eulektro.de", 200 + xOffset, 806, { lineBreak: false });
  doc.text("Bank: Olinda Zweigniederlassung Deutschland", 350 + xOffset, 798, {
    lineBreak: false,
  });
  doc.text("IBAN: DE41 1001 0123 3550 1278 32", 350 + xOffset, 806, { lineBreak: false });
  doc.text("BIC: QNTO DEB2 XXX", 350 + xOffset, 814, { lineBreak: false });
}

const writePageNumber = (doc: typeof PDFDocument) => {
  //Global Edits to All Pages (Header/Footer, etc)
  const pages = doc.bufferedPageRange();
  for (let i = 0; i < pages.count; i++) {
    doc.switchToPage(i);

    //Footer: Add page number
    const oldBottomMargin = doc.page.margins.bottom;
    doc.page.margins.bottom = 0; //Dumb: Have to remove bottom margin in order to write into it
    doc.fontSize(8);
    doc.text(`Seite: ${i + 1} von ${pages.count}`, 500, 790, {
      lineBreak: false,
    });
    doc.page.margins.bottom = oldBottomMargin; // ReProtect bottom margin
  }
};

function getInvoiceTypeText(kindOfInvoice: KindOfInvoice) {
  switch (kindOfInvoice) {
    case KindOfInvoice.STORNO:
      return "Stornorechnung";
    case KindOfInvoice.INVOICE:
      return "Rechnung";
    case KindOfInvoice.CREDIT:
      return "Gutschrift";
    default:
      return ""; // Oder einen anderen Standardwert
  }
}

export const createInvoicePDF = async ({ invoice, dir }: createInvoiceProps) => {
  let subject_addon = "";

  if (invoice?.contact?.providers.length === 1) {
    subject_addon = `(${invoice?.contact?.providers[0]?.providerCountryId}*${invoice?.contact?.providers[0]?.providerId})`;
  } else {
    subject_addon = "";
  }
  //TODO sprache Rechnung / Invoice
  let subject = `Invoice ${subject_addon}`;

  if (invoice.kindOfInvoice === KindOfInvoice.CREDIT) {
    subject = `Gutschrift ${subject_addon}`;
  }

  const invoiceNumber = invoice.invoiceNumber || "Preview";
  let contactNumber;
  if (invoice?.invoiceParent) {
    if (invoice.contact) {
      if (invoice?.invoiceParent.kindOfInvoice === KindOfInvoice.CREDIT) {
        contactNumber = invoice.contact?.supplierNumber;
      } else {
        contactNumber = invoice.contact?.customerNumber;
      }
    } else if (invoice?.user) {
      contactNumber = invoice.user.id;
    }
  } else {
    if (invoice.contact) {
      contactNumber =
        invoice.kindOfInvoice === KindOfInvoice.CREDIT
          ? invoice.contact.supplierNumber
          : invoice.contact.customerNumber;
    } else if (invoice?.user) {
      contactNumber = invoice.user.id;
    }
  }

  // Konvertieren Sie die servicePeriodFrom und servicePeriodTo Zeiten in lokale Zeit und formatieren Sie sie
  const from = invoice.servicePeriodFrom
    ? DateTime.fromJSDate(invoice.servicePeriodFrom, { zone: "UTC" })
        .setZone("Europe/Berlin")
        .toFormat("dd.MM.yyyy")
    : null;
  const to = invoice.servicePeriodTo
    ? DateTime.fromJSDate(invoice.servicePeriodTo, { zone: "UTC" })
        .setZone("Europe/Berlin")
        .toFormat("dd.MM.yyyy")
    : null;

  // Erstellen Sie den servicePeriod String
  const servicePeriod = from && to ? `${from} - ${to}` : null;

  if (!invoice.invoiceDate) {
    throw new Error("Error: no Invoice Date");
  }
  let contactAddress;
  if (invoice?.contact) {
    if (invoice.invoiceParent && invoice?.invoiceParent?.invoiceDate) {
      contactAddress = getContactAdressByDate(
        invoice.contact.contactAddress,
        invoice.invoiceParent.invoiceDate,
      );
    } else {
      contactAddress = getContactAdressByDate(invoice.contact.contactAddress, invoice.invoiceDate);
    }
  } else if (invoice?.user) {
    if (invoice.invoiceParent && invoice?.invoiceParent?.invoiceDate) {
      contactAddress = getContactAdressByDate(
        invoice.user.address,
        invoice.invoiceParent.invoiceDate,
      );
    } else {
      contactAddress = getContactAdressByDate(invoice?.user.address, invoice.invoiceDate);
    }
  }

  if (!contactAddress) {
    throw new Error("Error: no contactAddress");
  }
  if (!subject) {
    throw new Error("Error: no subject");
  }
  if (!invoiceNumber) {
    throw new Error("Fehler: Keine Rechnungsummer");
  }
  if (!contactNumber) {
    throw new Error("Fehler: Keine Kundenummer");
  }
  if (!servicePeriod || servicePeriod == "") {
    throw new Error("Fehler: Keine Laufzeit");
  }
  if (!invoice.invoicePositions || invoice.invoicePositions.length == 0) {
    throw new Error("Fehler: Keine Rechnungspositionen");
  }
  if (!invoice.invoiceDate) {
    throw new Error("Fehler: Keine Rechnungsdatum");
  }

  const date = formatDate(invoice.invoiceDate);
  const regexDot = /\./;
  let ust = contactAddress.isNetInvoice ? 0 : contactAddress.invoiceTaxRate || 0;
  if (invoice.sumTax != 0) {
    ust = 19;
  }
  let nettoSumPrice = 0.0;
  // Erstelle ein neues PDF-Dokument
  const doc = new PDFDocument({
    bufferPages: true,
    size: "A4",
    font: "public/OpenSans/OpenSans-Regular.ttf",
  });
  drawFooterAndLogo(doc);

  // Füge Logo hinzu
  doc.image("public/logo/EULEKTRO_21697c_R33_G105_B124.png", 36, 0, {
    height: 110,
  });

  // Füge den Rechnungstitel hinzu
  doc
    .font("public/OpenSans/OpenSans-Regular.ttf")
    .fontSize(18)
    .text(getInvoiceTypeText(invoice.kindOfInvoice), 260, 135, {
      width: 300,
      lineBreak: false,
      align: "right",
    });

  // Setze den Schriftstil und die Schriftgröße
  doc.font("public/OpenSans/OpenSans-Regular.ttf").fontSize(12);

  // Füge Eulektro-Adresse hinzu
  doc
    .font("public/OpenSans/OpenSans-Bold.ttf")
    .fontSize(8)
    .text("Eulektro GmbH", 50, 155, { align: "left", continued: true });
  doc
    .font("public/OpenSans/OpenSans-Regular.ttf")
    .fontSize(8)
    .text(" | Werderstraße 69 | 28199 Bremen");

  // Füge Customer-Adresse hinzu
  doc.moveDown();

  let name;
  if (invoice?.contact) {
    name = invoice?.contact?.companyName;
  } else if (invoice?.user && invoice?.user?.companyName) {
    name = invoice?.user?.companyName;
  } else if (invoice?.user && !invoice?.user?.companyName) {
    name = `${invoice?.user?.name} ${invoice?.user?.lastName}`;
  }

  doc.fontSize(10).text(`${name}`);
  doc.text(`${contactAddress.street} ${contactAddress.streetNr}`);
  doc.text(`${contactAddress.zip} ${contactAddress.city}`);
  doc.text(`${contactAddress.country}`);
  doc.moveDown();

  // Füge Rechnungsdetails hinzu
  doc.fontSize(10);
  doc.moveDown();

  doc.font("public/OpenSans/OpenSans-Bold.ttf");
  doc.y = 350;
  doc.text(`Betreff `, 50, doc.y);
  doc.moveUp();
  if (
    invoice.kindOfInvoice == KindOfInvoice.CREDIT ||
    invoice.kindOfInvoice == KindOfInvoice.CREDIT_STORNO
  ) {
    doc.text(`Gutschrift.-Nr. `, invoice?.contact ? 190 : 120, doc.y);
  } else {
    doc.text(`Rechn.-Nr. `, invoice?.contact ? 190 : 120, doc.y);
  }
  doc.moveUp();
  {
    (invoice.kindOfInvoice == KindOfInvoice.INVOICE ||
      invoice.kindOfInvoice == KindOfInvoice.STORNO) &&
      doc.text(`Kunden-Nr. `, invoice?.contact ? 300 : 230, doc.y);
  }
  {
    (invoice.kindOfInvoice == KindOfInvoice.CREDIT ||
      invoice.kindOfInvoice == KindOfInvoice.CREDIT_STORNO) &&
      doc.text(`Lief.-Nr. `, invoice?.contact ? 300 : 230, doc.y);
  }
  doc.moveUp();
  doc.text(`Leistungszeitraum `, 390, doc.y);
  doc.moveUp();
  doc.text(`Datum`, 528, doc.y, { align: "right" });
  doc.moveDown();
  // 95
  doc.font("public/OpenSans/OpenSans-Regular.ttf");
  doc.text(`${subject} `, 50, doc.y, { lineBreak: false });

  doc.text(`${invoiceNumber} `, invoice?.contact ? 190 : 120, doc.y, { lineBreak: false });

  doc.text(`${contactNumber} `, invoice?.contact ? 300 : 230, doc.y, { lineBreak: false });
  doc.text(`${servicePeriod} `, 390, doc.y, { lineBreak: false });
  doc.text(`${date} `, 512, 365, { lineBreak: false, align: "right" });
  doc.moveDown();
  // Füge eine Tabelle mit den Rechnungspositionen hinzu

  const maxPositionsFirstPage = invoice.kindOfInvoice == KindOfInvoice.INVOICE ? 6 : 12; // 7 passen auf Seite 1
  const maxPositionsPerPage = invoice.kindOfInvoice == KindOfInvoice.INVOICE ? 14 : 28; // 12 auf folgenden Seiten Passen Sie diese Zahl an, um die Anzahl der Positionen pro Seite zu steuern.
  const headerAndFooterHeight = 100; // Passen Sie diese Zahl an, um den Platz für Kopf- und Fußzeile zu steuern.

  drawTableHeader(doc, 400);

  doc.moveDown();
  doc.moveDown();

  invoice.invoicePositions.forEach((position, index) => {
    // Überprüfen Sie, ob ein Seitenumbruch erforderlich ist
    if (
      (index !== 0 && index <= maxPositionsFirstPage && index % maxPositionsFirstPage === 0) ||
      (index > maxPositionsFirstPage && (index - maxPositionsFirstPage) % maxPositionsPerPage === 0)
    ) {
      doc.addPage();
      drawFooterAndLogo(doc);
      doc.fontSize(10);
      drawTableHeader(doc, headerAndFooterHeight);
      doc.y = headerAndFooterHeight;
      doc.moveDown();
      doc.moveDown();
    }

    const totalPrice = position.sumGross;
    const totalPriceAsString = totalPrice.toFixed(2).replace(regexDot, ",");
    nettoSumPrice += position.sumNet;
    doc.font("public/OpenSans/OpenSans-Bold.ttf").text(`${index + 1}`, 50, doc.y, {});
    doc.moveUp();
    doc.font("public/OpenSans/OpenSans-Regular.ttf").text(`${position.description}`, 80, doc.y, {
      width: 200,
      lineBreak: true,
    });
    doc.moveUp();
    // Menge hinzufügen
    doc.text(`${position.amount.toFixed(2).replace(regexDot, ",")}`, 290, doc.y, {
      width: 50,
      lineBreak: false,
    });
    doc.moveUp();
    doc.text(`${position.unit}`, 350, doc.y);
    doc.moveUp();
    // MwSt. einfügen
    doc.text(`${ust}%`, 390, doc.y, {
      width: 22,
      align: "right",
      lineBreak: false,
    });
    doc.moveUp();
    // Einzelpreis hinzufügen
    doc.text(`${position.unitPrice.toFixed(2).replace(regexDot, ",")}`, 435, doc.y, {
      width: 55,
      align: "right",
      lineBreak: false,
    });
    doc.moveUp();
    // Gesamtpreis hinzufügen
    doc.text(`${totalPriceAsString}`, 504, doc.y, {
      width: 58,
      align: "right",
      lineBreak: false,
      lineGap: 5,
    });
  });
  const taxes = invoice.sumTax;
  const bruttoSumPriceWithTaxes = invoice.sumGross;
  doc.moveDown();
  doc.moveDown();
  doc.moveTo(50, doc.y).lineTo(563, doc.y).lineWidth(0.5).strokeColor("grey").stroke();
  // Netto hinzufügen
  doc.text("Netto", 380, doc.y);
  doc.moveUp();
  doc.text(`€ ${nettoSumPrice.toFixed(2).replace(regexDot, ",")}`, 450, doc.y, {
    align: "right",
    width: 112,
    lineBreak: false,
  });
  // MwSt. hinzufügen
  doc.text(`zzgl. ${ust}% MwSt.`, 335, doc.y);
  doc.moveUp();
  doc.text(`€ ${taxes.toFixed(2).replace(regexDot, ",")}`, 450, doc.y, {
    align: "right",
    width: 112,
    lineBreak: false,
  });
  // Gesamtpreis hinzufügen
  doc.font("public/OpenSans/OpenSans-Bold.ttf").text("Summe", 344, doc.y, {
    lineBreak: false,
  });

  doc.text(`€ ${bruttoSumPriceWithTaxes.toFixed(2).replace(regexDot, ",")}`, 344, doc.y, {
    align: "right",
    width: 218,
    lineBreak: false,
  });
  doc.moveDown();
  doc.moveDown();

  switch (invoice.kindOfInvoice) {
    case KindOfInvoice.INVOICE: {
      if (invoice.contact) {
        if (invoice.contact.stripeCustomerId) {
          doc.moveDown();
          doc.moveDown();
          doc.moveDown();
          doc
            .font("public/OpenSans/OpenSans-Regular.ttf")
            .text(
              "Der Betrag wird in den nächsten Tagen von ihrem hinterlegten Konto per Lastschriftmandat eingezogen.",
              50,
              doc.y,
              { align: "left" },
            );
        } else {
          doc
            .font("public/OpenSans/OpenSans-Regular.ttf")
            .text("Kontoverbindung:", 50, doc.y, { align: "left" });
          doc
            .font("public/OpenSans/OpenSans-Regular.ttf")
            .text("Bank: Olinda Zweigniederlassung Deutschland", 50, doc.y, {
              align: "left",
            });
          doc
            .font("public/OpenSans/OpenSans-Regular.ttf")
            .text("IBAN: DE41 1001 0123 3550 1278 32", 50, doc.y, {
              align: "left",
            });
          doc
            .font("public/OpenSans/OpenSans-Regular.ttf")
            .text("BIC: QNTODEB2XXX", 50, doc.y, { align: "left" });
          doc.moveDown();
          doc
            .font("public/OpenSans/OpenSans-Regular.ttf")
            .text(
              "Bitte überweisen Sie den Betrag innerhalb der nächsten 2 Wochen auf das unten genannte Konto.",
              50,
              doc.y,
              { align: "left" },
            );
        }
      } else if (invoice.user) {
        doc.moveDown();
        doc.moveDown();
        doc.moveDown();
        doc
          .font("public/OpenSans/OpenSans-Regular.ttf")
          .text(
            "Der Betrag wird in den nächsten Tagen von ihrem hinterlegten Konto per Lastschriftmandat eingezogen.",
            50,
            doc.y,
            { align: "left" },
          );
      }

      break;
    }
    case KindOfInvoice.STORNO: {
      doc
        .font("public/OpenSans/OpenSans-Regular.ttf")
        .text(
          `Diese Stornorechnung storniert Rechnung ${invoice?.invoiceParent?.invoiceNumber}`,
          50,
          doc.y,
          { align: "left" },
        );
      break;
    }
  }
  doc.moveDown();
  doc.moveDown();
  if (invoice.contact && contactAddress.isNetInvoice && invoice.sumTax == 0) {
    doc.text(
      "Reverse Charge:  Steuerschuldnerschaft des Leistungsempfängers” gemäß § 13b UStG.",
      50,
      doc.y,
    );
    if (contactAddress.country != "Deutschland") {
      doc.text(
        "The recipient of the service is liable for VAT according reverse charge mechanism.",
        50,
        doc.y,
      );
    }
  }
  if (invoice.contact && contactAddress.ustId) {
    doc.text(`UStID des Kunden: ${contactAddress.ustId}`, 50, doc.y, {
      align: "left",
    });
  }
  if (invoice.kindOfInvoice == KindOfInvoice.CREDIT) {
    doc
      .font("public/OpenSans/OpenSans-Regular.ttf")
      .text(
        `Details zu den einzelnen Ladevorgängen entnehmen Sie bitte der mitgesendeten CSV Datei`,
        50,
        doc.y,
        {
          align: "left",
        },
      );
  }

  writePageNumber(doc);

  const filePrefix = invoice.kindOfInvoice == KindOfInvoice.INVOICE ? "Invoice" : "Credit";
  const fileName = `${filePrefix}_${invoiceNumber}.pdf`;
  // Speichere das PDF-Dokument
  const pdf_path = `${dir}/${fileName}`;

  const stream = fs.createWriteStream(pdf_path);
  doc.pipe(stream);
  doc.end();

  await new Promise<void>((resolve) => {
    stream.on("finish", function () {
      resolve();
    });
  });
  const existingPdfBytes = await fs.readFileSync(pdf_path);

  // Lade das PDF-Dokument mit pdf-lib
  const pdfDoc = await plibPDFDocument.load(existingPdfBytes);

  const xmlString = createFinalZUGFeRDXML(invoice);

  const attachmentData = new TextEncoder().encode(xmlString);
  // Hänge die XML-Datei an das PDF an
  pdfDoc.attach(attachmentData, "factur-x.xml", {
    mimeType: "application/xml",
    description: `ZUGFeRD XML Invoice Data for invoice ${invoice.invoiceNumber}`,
  });

  const modifiedPdfBytes = await pdfDoc.save();

  //overwrite
  await fs.writeFileSync(pdf_path + ".zug.pdf", modifiedPdfBytes);

  const command = `gs -dPDFA=3 -dBATCH -dNOPAUSE -sColorConversionStrategy=RGB -sDEVICE=pdfwrite -sOutputFile=${
    pdf_path + ".pdfa.pdf"
  } PDFA_def.ps ${pdf_path + ".zug.pdf"}`;

  // Führe den Befehl aus
  const out = execSync(command);

  const xmpString = `
<?xpacket begin="﻿" id="W5M0MpCehiHzreSzNTczkc9d"?>
<x:xmpmeta xmlns:x="adobe:ns:meta/">
  <rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">
    <rdf:Description rdf:about=""
      xmlns:dc="http://purl.org/dc/elements/1.1/"
      xmlns:xmp="http://ns.adobe.com/xap/1.0/"
       xmlns:fx="urn:factur-x:pdfa:CrossIndustryDocument:invoice:2p1#"
        xmlns:pdfaid="http://www.aiim.org/pdfa/ns/id/">
       <fx:DocumentType>INVOICE</fx:DocumentType>
      <fx:Version>1.0</fx:Version>
      <fx:ConformanceLevel>EN 16931</fx:ConformanceLevel>
      <fx:DocumentFileName>ZUGFeRD-invoice.xml</fx:DocumentFileName>
      <pdfaid:part>3</pdfaid:part>
      <pdfaid:conformance>B</pdfaid:conformance>
    </rdf:Description>
  </rdf:RDF>
</x:xmpmeta>
<?xpacket end="w"?>
`;

  const existingPdfBytes2 = await fs.readFileSync(pdf_path + ".pdfa.pdf");

  // Lade das PDF-Dokument mit pdf-lib
  const pdfDoc2 = await plibPDFDocument.load(existingPdfBytes2);
  const metadata = pdfDoc2.context.lookup(pdfDoc2.catalog.get(PDFName.of("Metadata")));

  const xmpBytes = new TextEncoder().encode(xmpString);

  // Erstelle einen Metadatenstrom

  const metadataStream = pdfDoc2.context.register(pdfDoc2.context.stream(xmpBytes));

  pdfDoc2.catalog.set(PDFName.of("Metadata"), metadataStream);
  // Speichere das PDF mit dem Anhang

  const modifiedPdfBytes2 = await pdfDoc2.save();
  //overwrite
  await fs.writeFileSync(pdf_path + ".final.pdf", modifiedPdfBytes2);

  return pdf_path;
};
